// نماذج قاعدة البيانات المبسطة

/// نموذج الملف المفضل
class FavoriteItem {
  String id;
  String title;
  String path;
  String type; // 'audio' or 'video'
  String? artist;
  String? album;
  int? duration; // بالثواني
  DateTime dateAdded;
  String? thumbnail;

  FavoriteItem({
    required this.id,
    required this.title,
    required this.path,
    required this.type,
    this.artist,
    this.album,
    this.duration,
    required this.dateAdded,
    this.thumbnail,
  });

  /// تحويل من MediaItem
  factory FavoriteItem.fromMediaItem(dynamic mediaItem) {
    return FavoriteItem(
      id: mediaItem.id ?? '',
      title: mediaItem.title ?? 'مجهول',
      path: mediaItem.path,
      type: mediaItem.type.toString().split('.').last,
      artist: mediaItem.artist,
      album: mediaItem.album,
      duration: mediaItem.duration?.inSeconds,
      dateAdded: DateTime.now(),
      thumbnail: mediaItem.displayAlbum,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'path': path,
      'type': type,
      'artist': artist,
      'album': album,
      'duration': duration,
      'dateAdded': dateAdded.millisecondsSinceEpoch,
      'thumbnail': thumbnail,
    };
  }

  factory FavoriteItem.fromMap(Map<String, dynamic> map) {
    return FavoriteItem(
      id: map['id'] ?? '',
      title: map['title'] ?? 'مجهول',
      path: map['path'] ?? '',
      type: map['type'] ?? 'audio',
      artist: map['artist'],
      album: map['album'],
      duration: map['duration'],
      dateAdded: DateTime.fromMillisecondsSinceEpoch(map['dateAdded'] ?? 0),
      thumbnail: map['thumbnail'],
    );
  }
}

/// نموذج الملف المخفي
class HiddenItem {
  String id;
  String title;
  String path;
  String type;
  String? artist;
  String? album;
  int? duration;
  DateTime dateHidden;
  String? thumbnail;
  String? originalFolder;

  HiddenItem({
    required this.id,
    required this.title,
    required this.path,
    required this.type,
    this.artist,
    this.album,
    this.duration,
    required this.dateHidden,
    this.thumbnail,
    this.originalFolder,
  });

  factory HiddenItem.fromMediaItem(dynamic mediaItem) {
    return HiddenItem(
      id: mediaItem.id ?? '',
      title: mediaItem.title ?? 'مجهول',
      path: mediaItem.path,
      type: mediaItem.type.toString().split('.').last,
      artist: mediaItem.artist,
      album: mediaItem.album,
      duration: mediaItem.duration?.inSeconds,
      dateHidden: DateTime.now(),
      thumbnail: mediaItem.displayAlbum,
      originalFolder: mediaItem.path.split('/').reversed.skip(1).first,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'path': path,
      'type': type,
      'artist': artist,
      'album': album,
      'duration': duration,
      'dateHidden': dateHidden.millisecondsSinceEpoch,
      'thumbnail': thumbnail,
      'originalFolder': originalFolder,
    };
  }

  factory HiddenItem.fromMap(Map<String, dynamic> map) {
    return HiddenItem(
      id: map['id'] ?? '',
      title: map['title'] ?? 'مجهول',
      path: map['path'] ?? '',
      type: map['type'] ?? 'audio',
      artist: map['artist'],
      album: map['album'],
      duration: map['duration'],
      dateHidden: DateTime.fromMillisecondsSinceEpoch(map['dateHidden'] ?? 0),
      thumbnail: map['thumbnail'],
      originalFolder: map['originalFolder'],
    );
  }
}

/// نموذج قائمة التشغيل المخصصة
class CustomPlaylist {
  String id;
  String name;
  String description;
  List<String> itemIds;
  DateTime dateCreated;
  DateTime dateModified;
  String? coverImage;
  String type;

  CustomPlaylist({
    required this.id,
    required this.name,
    this.description = '',
    required this.itemIds,
    required this.dateCreated,
    required this.dateModified,
    this.coverImage,
    this.type = 'mixed',
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'itemIds': itemIds,
      'dateCreated': dateCreated.millisecondsSinceEpoch,
      'dateModified': dateModified.millisecondsSinceEpoch,
      'coverImage': coverImage,
      'type': type,
    };
  }

  factory CustomPlaylist.fromMap(Map<String, dynamic> map) {
    return CustomPlaylist(
      id: map['id'] ?? '',
      name: map['name'] ?? 'قائمة جديدة',
      description: map['description'] ?? '',
      itemIds: List<String>.from(map['itemIds'] ?? []),
      dateCreated: DateTime.fromMillisecondsSinceEpoch(map['dateCreated'] ?? 0),
      dateModified:
          DateTime.fromMillisecondsSinceEpoch(map['dateModified'] ?? 0),
      coverImage: map['coverImage'],
      type: map['type'] ?? 'mixed',
    );
  }
}

/// نموذج إعدادات التطبيق
class AppSettings {
  bool showHiddenFiles;
  String defaultSortOrder;
  bool autoPlayNext;
  bool showNotifications;
  double playbackSpeed;
  bool enableEqualizer;
  Map<String, double> equalizerSettings;
  bool enableLockScreen;
  bool enablePictureInPicture;
  String videoQuality;
  bool enableSubtitles;

  AppSettings({
    this.showHiddenFiles = false,
    this.defaultSortOrder = 'name',
    this.autoPlayNext = true,
    this.showNotifications = true,
    this.playbackSpeed = 1.0,
    this.enableEqualizer = false,
    this.equalizerSettings = const {},
    this.enableLockScreen = true,
    this.enablePictureInPicture = true,
    this.videoQuality = 'auto',
    this.enableSubtitles = false,
  });

  Map<String, dynamic> toMap() {
    return {
      'showHiddenFiles': showHiddenFiles,
      'defaultSortOrder': defaultSortOrder,
      'autoPlayNext': autoPlayNext,
      'showNotifications': showNotifications,
      'playbackSpeed': playbackSpeed,
      'enableEqualizer': enableEqualizer,
      'equalizerSettings': equalizerSettings,
      'enableLockScreen': enableLockScreen,
      'enablePictureInPicture': enablePictureInPicture,
      'videoQuality': videoQuality,
      'enableSubtitles': enableSubtitles,
    };
  }

  factory AppSettings.fromMap(Map<String, dynamic> map) {
    return AppSettings(
      showHiddenFiles: map['showHiddenFiles'] ?? false,
      defaultSortOrder: map['defaultSortOrder'] ?? 'name',
      autoPlayNext: map['autoPlayNext'] ?? true,
      showNotifications: map['showNotifications'] ?? true,
      playbackSpeed: (map['playbackSpeed'] ?? 1.0).toDouble(),
      enableEqualizer: map['enableEqualizer'] ?? false,
      equalizerSettings:
          Map<String, double>.from(map['equalizerSettings'] ?? {}),
      enableLockScreen: map['enableLockScreen'] ?? true,
      enablePictureInPicture: map['enablePictureInPicture'] ?? true,
      videoQuality: map['videoQuality'] ?? 'auto',
      enableSubtitles: map['enableSubtitles'] ?? false,
    );
  }
}

/// نموذج إحصائيات التشغيل
class PlaybackStats {
  String itemId;
  int playCount;
  DateTime lastPlayed;
  int totalPlayTime;
  DateTime firstPlayed;

  PlaybackStats({
    required this.itemId,
    this.playCount = 0,
    required this.lastPlayed,
    this.totalPlayTime = 0,
    required this.firstPlayed,
  });

  Map<String, dynamic> toMap() {
    return {
      'itemId': itemId,
      'playCount': playCount,
      'lastPlayed': lastPlayed.millisecondsSinceEpoch,
      'totalPlayTime': totalPlayTime,
      'firstPlayed': firstPlayed.millisecondsSinceEpoch,
    };
  }

  factory PlaybackStats.fromMap(Map<String, dynamic> map) {
    return PlaybackStats(
      itemId: map['itemId'] ?? '',
      playCount: map['playCount'] ?? 0,
      lastPlayed: DateTime.fromMillisecondsSinceEpoch(map['lastPlayed'] ?? 0),
      totalPlayTime: map['totalPlayTime'] ?? 0,
      firstPlayed: DateTime.fromMillisecondsSinceEpoch(map['firstPlayed'] ?? 0),
    );
  }
}
