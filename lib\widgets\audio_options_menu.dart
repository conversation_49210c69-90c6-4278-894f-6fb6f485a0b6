import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../utils/responsive_helper.dart';
import '../model/media_item.dart';
import '../services/simple_database_service.dart';

/// قائمة خيارات الصوت
class AudioOptionsMenu extends StatelessWidget {
  final MediaItem mediaItem;
  final List<MediaItem> playlist;
  final VoidCallback onPlay;
  final VoidCallback onAddToPlaylist;
  final VoidCallback onHide;
  final VoidCallback onDelete;
  final VoidCallback onShare;

  const AudioOptionsMenu({
    super.key,
    required this.mediaItem,
    required this.playlist,
    required this.onPlay,
    required this.onAddToPlaylist,
    required this.onHide,
    required this.onDelete,
    required this.onShare,
  });

  @override
  Widget build(BuildContext context) {
    final databaseService = DatabaseService.instance;

    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // مقبض السحب
          Container(
            width: 40,
            height: 4,
            margin: EdgeInsets.symmetric(
              vertical: ResponsiveHelper.getSpacing(context, SpacingType.sm),
            ),
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // معلومات الملف
          _buildFileInfo(context),

          Divider(
            height: ResponsiveHelper.getSpacing(context, SpacingType.lg),
            thickness: 1,
          ),

          // خيارات الملف
          _buildOptions(context, databaseService),

          SizedBox(
            height: ResponsiveHelper.getSpacing(context, SpacingType.lg),
          ),
        ],
      ),
    );
  }

  Widget _buildFileInfo(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(
        ResponsiveHelper.getSpacing(context, SpacingType.md),
      ),
      child: Row(
        children: [
          // أيقونة الصوت
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.music_note,
              color: Theme.of(context).primaryColor,
              size: ResponsiveHelper.getIconSize(context, IconSizeType.large),
            ),
          ),

          SizedBox(
            width: ResponsiveHelper.getSpacing(context, SpacingType.md),
          ),

          // معلومات الملف
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  mediaItem.title ?? 'مجهول',
                  style: TextStyle(
                    fontSize: ResponsiveHelper.getFontSize(
                      context,
                      FontSizeType.medium,
                    ),
                    fontWeight: FontWeight.bold,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                if (mediaItem.artist != null && mediaItem.artist!.isNotEmpty)
                  Text(
                    mediaItem.artist!,
                    style: TextStyle(
                      fontSize: ResponsiveHelper.getFontSize(
                        context,
                        FontSizeType.small,
                      ),
                      color: Colors.grey[600],
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                if (mediaItem.duration != null)
                  Text(
                    _formatDuration(mediaItem.duration!),
                    style: TextStyle(
                      fontSize: ResponsiveHelper.getFontSize(
                        context,
                        FontSizeType.small,
                      ),
                      color: Colors.grey[600],
                    ),
                  ),
              ],
            ),
          ),

          // زر التشغيل
          Container(
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor,
              shape: BoxShape.circle,
            ),
            child: IconButton(
              onPressed: () {
                Get.back();
                onPlay();
              },
              icon: Icon(
                Icons.play_arrow,
                color: Colors.white,
                size: ResponsiveHelper.getIconSize(context, IconSizeType.large),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOptions(BuildContext context, DatabaseService databaseService) {
    return Column(
      children: [
        // إضافة للمفضلة
        Obx(() => _buildOptionTile(
              context,
              icon: databaseService.isFavorite(mediaItem.id ?? '')
                  ? Icons.favorite
                  : Icons.favorite_border,
              title: databaseService.isFavorite(mediaItem.id ?? '')
                  ? 'إزالة من المفضلة'
                  : 'إضافة للمفضلة',
              subtitle: 'حفظ في قائمة المفضلة',
              onTap: () {
                Get.back();
                _toggleFavorite(databaseService);
              },
              iconColor: databaseService.isFavorite(mediaItem.id ?? '')
                  ? Colors.red
                  : null,
            )),

        // إضافة لقائمة تشغيل
        _buildOptionTile(
          context,
          icon: Icons.playlist_add,
          title: 'إضافة لقائمة تشغيل',
          subtitle: 'إضافة لقائمة تشغيل مخصصة',
          onTap: () {
            Get.back();
            onAddToPlaylist();
          },
        ),

        // إخفاء الملف
        Obx(() => _buildOptionTile(
              context,
              icon: databaseService.isHidden(mediaItem.id ?? '')
                  ? Icons.visibility
                  : Icons.visibility_off,
              title: databaseService.isHidden(mediaItem.id ?? '')
                  ? 'إظهار الملف'
                  : 'إخفاء الملف',
              subtitle: databaseService.isHidden(mediaItem.id ?? '')
                  ? 'إظهار الملف في القائمة'
                  : 'إخفاء الملف من القائمة',
              onTap: () {
                Get.back();
                if (databaseService.isHidden(mediaItem.id ?? '')) {
                  databaseService.unhideItem(mediaItem.id ?? '');
                  Get.snackbar('تم', 'تم إظهار الملف');
                } else {
                  onHide();
                }
              },
            )),

        // مشاركة الملف
        _buildOptionTile(
          context,
          icon: Icons.share,
          title: 'مشاركة',
          subtitle: 'مشاركة الملف مع التطبيقات الأخرى',
          onTap: () {
            Get.back();
            onShare();
          },
        ),

        // معلومات الملف
        _buildOptionTile(
          context,
          icon: Icons.info_outline,
          title: 'معلومات الملف',
          subtitle: 'عرض تفاصيل الملف',
          onTap: () {
            Get.back();
            _showFileInfo(context);
          },
        ),

        // حذف الملف
        _buildOptionTile(
          context,
          icon: Icons.delete_outline,
          title: 'حذف الملف',
          subtitle: 'حذف الملف نهائياً من الجهاز',
          onTap: () {
            Get.back();
            onDelete();
          },
          iconColor: Colors.red,
          titleColor: Colors.red,
        ),
      ],
    );
  }

  Widget _buildOptionTile(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    Color? iconColor,
    Color? titleColor,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: iconColor ?? Theme.of(context).iconTheme.color,
        size: ResponsiveHelper.getIconSize(context, IconSizeType.medium),
      ),
      title: Text(
        title,
        style: TextStyle(
          fontSize: ResponsiveHelper.getFontSize(context, FontSizeType.medium),
          color: titleColor,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          fontSize: ResponsiveHelper.getFontSize(context, FontSizeType.small),
        ),
      ),
      onTap: onTap,
    );
  }

  Future<void> _toggleFavorite(DatabaseService databaseService) async {
    if (databaseService.isFavorite(mediaItem.id ?? '')) {
      await databaseService.removeFromFavorites(mediaItem.id ?? '');
      Get.snackbar('تم', 'تم إزالة الملف من المفضلة');
    } else {
      await databaseService.addToFavorites(mediaItem);
      Get.snackbar('تم', 'تم إضافة الملف للمفضلة');
    }
  }

  void _showFileInfo(BuildContext context) {
    Get.dialog(
      AlertDialog(
        title: const Text('معلومات الملف'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildInfoRow('الاسم:', mediaItem.title ?? 'مجهول'),
            _buildInfoRow('الفنان:', mediaItem.artist ?? 'مجهول'),
            _buildInfoRow('الألبوم:', mediaItem.album ?? 'مجهول'),
            _buildInfoRow(
                'المدة:', _formatDuration(mediaItem.duration ?? Duration.zero)),
            _buildInfoRow('المسار:', mediaItem.path),
            _buildInfoRow('النوع:', 'صوت'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 60,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(color: Colors.grey),
            ),
          ),
        ],
      ),
    );
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }
}
