import 'dart:io';
import 'package:get/get.dart';
import 'package:just_audio/just_audio.dart';
import 'package:audio_session/audio_session.dart';
import '../model/media_item.dart' as local;

/// خدمة تشغيل الصوت المبسطة بدون AudioService
class SimpleAudioPlayerService extends GetxController {
  static SimpleAudioPlayerService get instance =>
      Get.find<SimpleAudioPlayerService>();

  final AudioPlayer _audioPlayer = AudioPlayer();

  // حالة التشغيل
  var isPlaying = false.obs;
  var isLoading = false.obs;
  var currentPosition = Duration.zero.obs;
  var totalDuration = Duration.zero.obs;
  var playbackSpeed = 1.0.obs;

  // حالة واجهة المشغل (مرئي، مصغر، كامل)
  var isPlayerVisible = true.obs;
  var isMinimized = false.obs;
  var isFullScreen = true.obs;

  // قائمة التشغيل والفهرس الحالي
  var currentPlaylist = <local.MediaItem>[].obs;
  var currentIndex = 0.obs;
  var currentMediaItem = Rxn<local.MediaItem>();

  // إعدادات التشغيل
  var repeatMode = RepeatMode.none.obs;
  var isShuffleEnabled = false.obs;
  var shuffledIndices = <int>[].obs;

  // إعدادات الفرز والفلترة
  var sortOrder = SortOrder.name.obs;
  var isAscending = true.obs;
  var currentPlaylistName = 'جميع الأغاني'.obs;
  var currentPlaylistId = ''.obs; // معرف قائمة التشغيل الحالية

  // نظام السجل المحسن
  String _lastPlayedTrackId = '';
  DateTime _lastPlayTime = DateTime.now();

  @override
  void onInit() {
    super.onInit();
    _initializePlayer();
  }

  @override
  void onClose() {
    _audioPlayer.dispose();
    super.onClose();
  }

  /// تهيئة المشغل
  Future<void> _initializePlayer() async {
    try {
      // تهيئة جلسة الصوت
      final session = await AudioSession.instance;
      await session.configure(const AudioSessionConfiguration.music());

      // ربط الـ streams
      _setupStreams();
    } catch (e) {
      print('خطأ في تهيئة المشغل: $e');
    }
  }

  /// ربط الـ streams
  void _setupStreams() {
    // ربط حالة التشغيل
    _audioPlayer.playingStream.listen((playing) {
      isPlaying.value = playing;
    });

    // ربط الموضع
    _audioPlayer.positionStream.listen((position) {
      currentPosition.value = position;
    });

    // ربط المدة الإجمالية
    _audioPlayer.durationStream.listen((duration) {
      if (duration != null) totalDuration.value = duration;
    });

    // ربط حالة المشغل
    _audioPlayer.playerStateStream.listen((state) {
      isLoading.value = state.processingState == ProcessingState.loading;

      if (state.processingState == ProcessingState.completed) {
        _onTrackCompleted();
      }
    });
  }

  /// معالجة انتهاء التشغيل
  void _onTrackCompleted() {
    switch (repeatMode.value) {
      case RepeatMode.one:
        _audioPlayer.seek(Duration.zero);
        _audioPlayer.play();
        break;
      case RepeatMode.all:
        playNext();
        break;
      case RepeatMode.none:
        if (currentIndex.value < currentPlaylist.length - 1) {
          playNext();
        } else {
          stop();
        }
        break;
    }
  }

  /// تشغيل ملف صوتي
  Future<void> playMediaItem(local.MediaItem item,
      {List<local.MediaItem>? playlist, String? playlistName}) async {
    try {
      isLoading.value = true;

      // التحقق من وجود الملف
      final file = File(item.path);
      if (!await file.exists()) {
        Get.snackbar(
          'خطأ',
          'الملف غير موجود: ${item.title}',
          snackPosition: SnackPosition.BOTTOM,
        );
        return;
      }

      if (playlist != null) {
        currentPlaylist.assignAll(playlist);
        currentIndex.value =
            playlist.indexWhere((media) => media.id == item.id);
        if (currentIndex.value == -1) currentIndex.value = 0;
      } else {
        currentPlaylist.assignAll([item]);
        currentIndex.value = 0;
      }

      if (playlistName != null) {
        currentPlaylistName.value = playlistName;
        // حفظ معرف قائمة التشغيل إذا كان متاحاً
        currentPlaylistId.value = playlistName;
      }

      currentMediaItem.value = item;

      // إيقاف التشغيل الحالي أولاً
      await _audioPlayer.stop();

      try {
        // تشغيل الملف مع معالجة أفضل للأخطاء
        print('محاولة تشغيل الملف: ${item.path}');

        // التأكد من أن المسار صحيح
        final uri = Uri.file(item.path);
        print('URI: $uri');

        await _audioPlayer.setAudioSource(
          AudioSource.uri(uri),
          preload: false, // عدم التحميل المسبق لتجنب مشاكل الذاكرة
        );

        await _audioPlayer.play();
        print('تم بدء التشغيل بنجاح');
      } catch (audioError) {
        print('خطأ في تشغيل الصوت: $audioError');

        // محاولة أخرى بطريقة مختلفة
        try {
          await _audioPlayer.setFilePath(item.path);
          await _audioPlayer.play();
          print('تم التشغيل بالطريقة البديلة');
        } catch (fallbackError) {
          print('فشل في التشغيل البديل: $fallbackError');
          throw audioError; // إرجاع الخطأ الأصلي
        }
      }

      showPlayer();
    } catch (e) {
      print('خطأ في تشغيل الملف: $e');
      Get.snackbar(
        'خطأ في التشغيل',
        'فشل في تشغيل الملف: ${item.title}\nالخطأ: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        duration: const Duration(seconds: 5),
      );
    } finally {
      isLoading.value = false;
    }
  }

  /// التبديل بين تشغيل/إيقاف مؤقت
  Future<void> togglePlayPause() async {
    if (isPlaying.value) {
      await _audioPlayer.pause();
    } else {
      await _audioPlayer.play();
    }
  }

  /// تشغيل الملف التالي
  Future<void> playNext() async {
    validateAndFixCurrentIndex();

    if (currentPlaylist.isEmpty) {
      print('قائمة التشغيل فارغة');
      return;
    }

    int nextIndex;
    if (isShuffleEnabled.value) {
      nextIndex = _getNextShuffledIndex();
    } else {
      nextIndex = (currentIndex.value + 1) % currentPlaylist.length;
    }

    await safePlayAtIndex(nextIndex);
  }

  /// تشغيل الملف السابق
  Future<void> playPrevious() async {
    validateAndFixCurrentIndex();

    if (currentPlaylist.isEmpty) {
      print('قائمة التشغيل فارغة');
      return;
    }

    int prevIndex;
    if (isShuffleEnabled.value) {
      prevIndex = _getPreviousShuffledIndex();
    } else {
      prevIndex = currentIndex.value - 1;
      if (prevIndex < 0) prevIndex = currentPlaylist.length - 1;
    }

    await safePlayAtIndex(prevIndex);
  }

  /// الانتقال إلى موضع معين
  Future<void> seekTo(Duration position) async {
    await _audioPlayer.seek(position);
  }

  /// تغيير سرعة التشغيل
  Future<void> setPlaybackSpeed(double speed) async {
    playbackSpeed.value = speed;
    await _audioPlayer.setSpeed(speed);
  }

  /// تبديل وضع التكرار
  Future<void> toggleRepeatMode() async {
    switch (repeatMode.value) {
      case RepeatMode.none:
        repeatMode.value = RepeatMode.one;
        break;
      case RepeatMode.one:
        repeatMode.value = RepeatMode.all;
        break;
      case RepeatMode.all:
        repeatMode.value = RepeatMode.none;
        break;
    }
  }

  /// تبديل وضع الخلط
  Future<void> toggleShuffle() async {
    isShuffleEnabled.value = !isShuffleEnabled.value;
    if (isShuffleEnabled.value) {
      _generateShuffledIndices();
    }
  }

  /// توليد فهارس عشوائية
  void _generateShuffledIndices() {
    shuffledIndices.clear();
    shuffledIndices
        .addAll(List.generate(currentPlaylist.length, (index) => index));
    shuffledIndices.shuffle();
  }

  /// الحصول على الفهرس التالي في وضع العشوائي
  int _getNextShuffledIndex() {
    if (shuffledIndices.isEmpty) _generateShuffledIndices();
    final currentShuffledIndex = shuffledIndices.indexOf(currentIndex.value);
    return shuffledIndices[(currentShuffledIndex + 1) % shuffledIndices.length];
  }

  /// الحصول على الفهرس السابق في وضع العشوائي
  int _getPreviousShuffledIndex() {
    if (shuffledIndices.isEmpty) _generateShuffledIndices();
    final currentShuffledIndex = shuffledIndices.indexOf(currentIndex.value);
    int prevIndex = currentShuffledIndex - 1;
    if (prevIndex < 0) prevIndex = shuffledIndices.length - 1;
    return shuffledIndices[prevIndex];
  }

  /// إيقاف التشغيل
  Future<void> stop() async {
    await _audioPlayer.stop();
    // hidePlayer();
  }

  /// إظهار المشغل
  /// إظهار المشغل
  void showPlayer({bool fullScreen = true}) {
    isPlayerVisible.value = true;
    if (fullScreen) {
      isMinimized.value = false;
      isFullScreen.value = true;
    } else {
      isMinimized.value = true;
      isFullScreen.value = false;
    }
  }

  /// إخفاء المشغل
  void hidePlayer() {
    isPlayerVisible.value = false;
    isMinimized.value = true;
    isFullScreen.value = false;
  }

  /// تبديل حجم المشغل
  void togglePlayerSize() {
    if (isFullScreen.value) {
      isFullScreen.value = false;
      isMinimized.value = true;
    } else {
      isFullScreen.value = true;
      isMinimized.value = false;
    }
  }

  /// تعيين حجم المشغل
  void setPlayerSize({required bool fullScreen}) {
    isFullScreen.value = fullScreen;
    isMinimized.value = !fullScreen;
  }

  /// ترتيب قائمة التشغيل
  void sortPlaylist(SortOrder order) {
    switch (order) {
      case SortOrder.name:
        currentPlaylist
            .sort((a, b) => (a.title ?? '').compareTo(b.title ?? ''));
        break;
      case SortOrder.artist:
        currentPlaylist
            .sort((a, b) => (a.artist ?? '').compareTo(b.artist ?? ''));
        break;
      case SortOrder.album:
        currentPlaylist
            .sort((a, b) => (a.album ?? '').compareTo(b.album ?? ''));
        break;
      case SortOrder.duration:
        currentPlaylist.sort((a, b) =>
            (a.duration?.inSeconds ?? 0).compareTo(b.duration?.inSeconds ?? 0));
        break;
      case SortOrder.dateAdded:
        currentPlaylist.sort((a, b) => (a.dateAdded ?? DateTime.now())
            .compareTo(b.dateAdded ?? DateTime.now()));
        break;
    }
  }

  /// حارس أمان لتشغيل الملف بفهرس آمن
  Future<void> safePlayAtIndex(int index) async {
    if (currentPlaylist.isEmpty) {
      print('القائمة فارغة، لا يمكن التشغيل');
      return;
    }

    // التأكد من صحة الفهرس
    final safeIndex = index.clamp(0, currentPlaylist.length - 1);
    if (safeIndex != index) {
      print('تم تصحيح الفهرس من $index إلى $safeIndex');
    }

    // تحديث الفهرس فقط إذا كان مختلفاً
    if (currentIndex.value != safeIndex) {
      currentIndex.value = safeIndex;
      currentMediaItem.value = currentPlaylist[safeIndex];

      // تشغيل الملف الجديد مباشرة بدون استدعاء playMediaItem
      await _playDirectly(currentPlaylist[safeIndex]);
    }
  }

  /// تشغيل مباشر للملف بدون إعادة تعيين القائمة
  Future<void> _playDirectly(local.MediaItem item) async {
    // منع التشغيل المتكرر لنفس الملف
    final currentTime = DateTime.now();
    if (_lastPlayedTrackId == item.id &&
        currentTime.difference(_lastPlayTime).inMilliseconds < 1000) {
      print('تم تجاهل التشغيل المتكرر للملف: ${item.title}');
      return;
    }

    try {
      isLoading.value = true;
      _lastPlayedTrackId = item.id ?? '';
      _lastPlayTime = currentTime;

      print('بدء تشغيل مباشر: ${item.title}');

      // إيقاف التشغيل الحالي أولاً
      await _audioPlayer.stop();

      try {
        // تشغيل الملف مع معالجة أفضل للأخطاء
        final uri = Uri.file(item.path);

        await _audioPlayer.setAudioSource(
          AudioSource.uri(uri),
          preload: false,
        );

        await _audioPlayer.play();
        print('تم بدء التشغيل بنجاح: ${item.title}');
      } catch (audioError) {
        print('خطأ في setAudioSource، محاولة setFilePath: $audioError');
        // محاولة أخرى بطريقة مختلفة
        try {
          await _audioPlayer.setFilePath(item.path);
          await _audioPlayer.play();
          print('تم التشغيل بالطريقة البديلة: ${item.title}');
        } catch (fallbackError) {
          print('فشل في كلا الطريقتين: $fallbackError');
          throw audioError;
        }
      }

      showPlayer(fullScreen: true);
    } catch (e) {
      print('خطأ في التشغيل المباشر: $e');
      Get.snackbar(
        'خطأ في التشغيل',
        'فشل في تشغيل الملف: ${item.title}',
        snackPosition: SnackPosition.BOTTOM,
        duration: const Duration(seconds: 3),
      );
    } finally {
      isLoading.value = false;
    }
  }

  /// حارس أمان للحصول على عنصر بفهرس آمن
  local.MediaItem? safeGetItemAtIndex(int index) {
    if (currentPlaylist.isEmpty ||
        index < 0 ||
        index >= currentPlaylist.length) {
      return null;
    }
    return currentPlaylist[index];
  }

  /// التحقق من صحة الفهرس الحالي وإصلاحه إذا لزم الأمر
  void validateAndFixCurrentIndex() {
    if (currentPlaylist.isEmpty) {
      currentIndex.value = 0;
      currentMediaItem.value = null;
      return;
    }

    if (currentIndex.value < 0 ||
        currentIndex.value >= currentPlaylist.length) {
      final oldIndex = currentIndex.value;
      currentIndex.value = 0.clamp(0, currentPlaylist.length - 1);
      print('تم إصلاح الفهرس من $oldIndex إلى ${currentIndex.value}');

      // تحديث العنصر الحالي
      if (currentPlaylist.isNotEmpty) {
        currentMediaItem.value = currentPlaylist[currentIndex.value];
      }
    }
  }

  // Getters للمعلومات الحالية
  String get currentTitle => currentMediaItem.value?.title ?? '';
  String get currentArtist => currentMediaItem.value?.artist ?? 'مجهول';
  String get currentAlbum => currentMediaItem.value?.album ?? 'مجهول';
  bool get hasCurrentTrack => currentMediaItem.value != null;
  String get formattedPosition => _formatDuration(currentPosition.value);
  String get formattedDuration => _formatDuration(totalDuration.value);

  /// تنسيق الوقت
  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }
}

/// أنواع التكرار
enum RepeatMode { none, one, all }

/// أنواع الترتيب
enum SortOrder { name, artist, album, duration, dateAdded }
