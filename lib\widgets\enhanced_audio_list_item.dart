import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:share_plus/share_plus.dart';
import 'dart:io';
import '../utils/responsive_helper.dart';
import '../model/media_item.dart';
import '../services/database_service.dart';
import '../services/simple_audio_player_service.dart';
import 'audio_options_menu.dart';

/// عنصر قائمة الصوت المحسن مع جميع المميزات
class EnhancedAudioListItem extends StatelessWidget {
  final MediaItem mediaItem;
  final List<MediaItem> playlist;
  final int index;
  final VoidCallback? onTap;
  final bool showIndex;

  const EnhancedAudioListItem({
    super.key,
    required this.mediaItem,
    required this.playlist,
    required this.index,
    this.onTap,
    this.showIndex = true,
  });

  @override
  Widget build(BuildContext context) {
    final databaseService = DatabaseService.instance;
    final playerService = SimpleAudioPlayerService.instance;

    return Obx(() {
      final isCurrentlyPlaying =
          playerService.currentMediaItem.value?.id == mediaItem.id;
      final isFavorite = databaseService.isFavorite(mediaItem.id ?? '');
      final isHidden = databaseService.isHidden(mediaItem.id ?? '');

      // إخفاء العنصر إذا كان مخفياً
      if (isHidden) return const SizedBox.shrink();

      return Container(
        margin: EdgeInsets.symmetric(
          horizontal: ResponsiveHelper.getSpacing(context, SpacingType.sm),
          vertical: ResponsiveHelper.getSpacing(context, SpacingType.xs),
        ),
        decoration: BoxDecoration(
          color: isCurrentlyPlaying
              ? Theme.of(context).primaryColor.withOpacity(0.1)
              : null,
          borderRadius: BorderRadius.circular(12),
          border: isCurrentlyPlaying
              ? Border.all(
                  color: Theme.of(context).primaryColor,
                  width: 2,
                )
              : null,
        ),
        child: ListTile(
          contentPadding: EdgeInsets.symmetric(
            horizontal: ResponsiveHelper.getSpacing(context, SpacingType.md),
            vertical: ResponsiveHelper.getSpacing(context, SpacingType.xs),
          ),

          // الرقم أو أيقونة التشغيل
          leading: _buildLeading(context, isCurrentlyPlaying, playerService),

          // معلومات الملف
          title: Text(
            mediaItem.title ?? 'مجهول',
            style: TextStyle(
              fontSize:
                  ResponsiveHelper.getFontSize(context, FontSizeType.medium),
              fontWeight:
                  isCurrentlyPlaying ? FontWeight.bold : FontWeight.normal,
              color: isCurrentlyPlaying ? Theme.of(context).primaryColor : null,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),

          subtitle: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (mediaItem.artist != null && mediaItem.artist!.isNotEmpty)
                Text(
                  mediaItem.artist!,
                  style: TextStyle(
                    fontSize: ResponsiveHelper.getFontSize(
                        context, FontSizeType.small),
                    color: Colors.grey[600],
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              Row(
                children: [
                  if (mediaItem.duration != null)
                    Text(
                      _formatDuration(mediaItem.duration!),
                      style: TextStyle(
                        fontSize: ResponsiveHelper.getFontSize(
                            context, FontSizeType.small),
                        color: Colors.grey[500],
                      ),
                    ),
                  if (mediaItem.duration != null && mediaItem.album != null)
                    Text(
                      ' • ',
                      style: TextStyle(
                        fontSize: ResponsiveHelper.getFontSize(
                            context, FontSizeType.small),
                        color: Colors.grey[500],
                      ),
                    ),
                  if (mediaItem.album != null && mediaItem.album!.isNotEmpty)
                    Expanded(
                      child: Text(
                        mediaItem.album!,
                        style: TextStyle(
                          fontSize: ResponsiveHelper.getFontSize(
                              context, FontSizeType.small),
                          color: Colors.grey[500],
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                ],
              ),
            ],
          ),

          // أزرار التحكم
          trailing: _buildTrailing(
              context, isFavorite, isCurrentlyPlaying, playerService),

          onTap: onTap ?? () => _playAudio(context),
        ),
      );
    });
  }

  Widget _buildLeading(BuildContext context, bool isCurrentlyPlaying,
      SimpleAudioPlayerService playerService) {
    if (isCurrentlyPlaying) {
      return Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: Theme.of(context).primaryColor,
          shape: BoxShape.circle,
        ),
        child: Obx(() => IconButton(
              onPressed: () => playerService.togglePlayPause(),
              icon: Icon(
                playerService.isPlaying.value ? Icons.pause : Icons.play_arrow,
                color: Colors.white,
                size:
                    ResponsiveHelper.getIconSize(context, IconSizeType.medium),
              ),
              padding: EdgeInsets.zero,
            )),
      );
    }

    if (showIndex) {
      return Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: Theme.of(context).primaryColor.withOpacity(0.1),
          shape: BoxShape.circle,
        ),
        child: Center(
          child: Text(
            '${index + 1}',
            style: TextStyle(
              fontSize:
                  ResponsiveHelper.getFontSize(context, FontSizeType.medium),
              fontWeight: FontWeight.bold,
              color: Theme.of(context).primaryColor,
            ),
          ),
        ),
      );
    }

    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor.withOpacity(0.1),
        shape: BoxShape.circle,
      ),
      child: Icon(
        Icons.music_note,
        color: Theme.of(context).primaryColor,
        size: ResponsiveHelper.getIconSize(context, IconSizeType.medium),
      ),
    );
  }

  Widget _buildTrailing(BuildContext context, bool isFavorite,
      bool isCurrentlyPlaying, SimpleAudioPlayerService playerService) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // أيقونة المفضلة
        IconButton(
          onPressed: () => _toggleFavorite(),
          icon: Icon(
            isFavorite ? Icons.favorite : Icons.favorite_border,
            color: isFavorite ? Colors.red : Colors.grey,
            size: ResponsiveHelper.getIconSize(context, IconSizeType.medium),
          ),
          padding: EdgeInsets.zero,
          constraints: const BoxConstraints(),
        ),

        SizedBox(width: ResponsiveHelper.getSpacing(context, SpacingType.xs)),

        // زر الخيارات
        IconButton(
          onPressed: () => _showOptionsMenu(context),
          icon: Icon(
            Icons.more_vert,
            color: Colors.grey,
            size: ResponsiveHelper.getIconSize(context, IconSizeType.medium),
          ),
          padding: EdgeInsets.zero,
          constraints: const BoxConstraints(),
        ),
      ],
    );
  }

  void _playAudio(BuildContext context) {
    final playerService = SimpleAudioPlayerService.instance;

    // إيقاف التشغيل الحالي إذا كان نفس الملف
    if (playerService.currentMediaItem.value?.id == mediaItem.id &&
        playerService.isPlaying.value) {
      playerService.togglePlayPause();
      return;
    }

    // تشغيل الملف الجديد
    playerService.playMediaItem(
      mediaItem,
      playlist: playlist,
      playlistName: 'قائمة التشغيل',
    );

    // إظهار المشغل
    // إظهار المشغل الكامل عند تشغيل مقطع جديد
    playerService.showPlayer(fullScreen: true);
  }

  Future<void> _toggleFavorite() async {
    final databaseService = DatabaseService.instance;

    if (databaseService.isFavorite(mediaItem.id ?? '')) {
      await databaseService.removeFromFavorites(mediaItem.id ?? '');
      Get.snackbar('تم', 'تم إزالة الملف من المفضلة');
    } else {
      await databaseService.addToFavorites(mediaItem);
      Get.snackbar('تم', 'تم إضافة الملف للمفضلة');
    }
  }

  void _showOptionsMenu(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => AudioOptionsMenu(
        mediaItem: mediaItem,
        playlist: playlist,
        onPlay: () => _playAudio(context),
        onAddToPlaylist: () => _addToPlaylist(),
        onHide: () => _hideFile(),
        onDelete: () => _deleteFile(),
        onShare: () => _shareFile(),
      ),
    );
  }

  void _addToPlaylist() {
    final databaseService = DatabaseService.instance;

    Get.bottomSheet(
      Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Get.theme.scaffoldBackgroundColor,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'إضافة إلى قائمة التشغيل',
              style: Get.textTheme.headlineSmall,
            ),
            const SizedBox(height: 16),

            // إنشاء قائمة جديدة
            ListTile(
              leading: const Icon(Icons.add),
              title: const Text('إنشاء قائمة جديدة'),
              onTap: () {
                Get.back();
                _createNewPlaylist();
              },
            ),

            const Divider(),

            // عرض قوائم التشغيل الموجودة
            Obx(() {
              final playlists = databaseService.customPlaylists;
              if (playlists.isEmpty) {
                return const Padding(
                  padding: EdgeInsets.all(16.0),
                  child: Text('لا توجد قوائم تشغيل'),
                );
              }

              return ListView.builder(
                shrinkWrap: true,
                itemCount: playlists.length,
                itemBuilder: (context, index) {
                  final playlist = playlists[index];
                  return ListTile(
                    leading: const Icon(Icons.queue_music),
                    title: Text(playlist.name),
                    subtitle: Text('${playlist.itemIds.length} ملف'),
                    onTap: () {
                      Get.back();
                      _addToExistingPlaylist(playlist.id);
                    },
                  );
                },
              );
            }),
          ],
        ),
      ),
    );
  }

  void _createNewPlaylist() {
    final TextEditingController nameController = TextEditingController();

    Get.dialog(
      AlertDialog(
        title: const Text('إنشاء قائمة تشغيل جديدة'),
        content: TextField(
          controller: nameController,
          decoration: const InputDecoration(
            labelText: 'اسم قائمة التشغيل',
            hintText: 'أدخل اسم القائمة',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () async {
              final name = nameController.text.trim();
              if (name.isNotEmpty) {
                final databaseService = DatabaseService.instance;
                final success = await databaseService.createPlaylist(
                  name,
                  description: 'قائمة تشغيل مخصصة',
                  type: 'audio',
                );

                if (success) {
                  Get.back();
                  // إضافة الملف إلى القائمة الجديدة
                  final newPlaylist = databaseService.customPlaylists.last;
                  await databaseService.addToPlaylist(
                      newPlaylist.id, mediaItem.id ?? '');
                  Get.snackbar('تم', 'تم إنشاء قائمة التشغيل وإضافة الملف');
                } else {
                  Get.snackbar('خطأ', 'فشل في إنشاء قائمة التشغيل');
                }
              }
            },
            child: const Text('إنشاء'),
          ),
        ],
      ),
    );
  }

  void _addToExistingPlaylist(String playlistId) async {
    final databaseService = DatabaseService.instance;
    final success =
        await databaseService.addToPlaylist(playlistId, mediaItem.id ?? '');

    if (success) {
      Get.snackbar('تم', 'تم إضافة الملف إلى قائمة التشغيل');
    } else {
      Get.snackbar('خطأ', 'فشل في إضافة الملف إلى قائمة التشغيل');
    }
  }

  Future<void> _hideFile() async {
    final databaseService = DatabaseService.instance;
    final success = await databaseService.hideItem(mediaItem);

    if (success) {
      Get.snackbar('تم', 'تم إخفاء الملف');
    } else {
      Get.snackbar('خطأ', 'فشل في إخفاء الملف');
    }
  }

  void _deleteFile() {
    Get.dialog(
      AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل تريد حذف "${mediaItem.title}" نهائياً؟'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              // TODO: تنفيذ حذف الملف
              Get.snackbar('قريباً', 'ميزة الحذف ستكون متاحة قريباً');
            },
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  void _shareFile() async {
    try {
      final file = File(mediaItem.path);
      if (await file.exists()) {
        // مشاركة الملف مع معلوماته
        await Share.shareXFiles(
          [XFile(mediaItem.path)],
          text: 'مشاركة ملف صوتي: ${mediaItem.title ?? 'مجهول'}\n'
              'الفنان: ${mediaItem.artist ?? 'مجهول'}\n'
              'الألبوم: ${mediaItem.album ?? 'مجهول'}',
          subject: 'ملف صوتي - ${mediaItem.title ?? 'مجهول'}',
        );
      } else {
        Get.snackbar(
          'خطأ',
          'الملف غير موجود',
          snackPosition: SnackPosition.BOTTOM,
        );
      }
    } catch (e) {
      print('خطأ في مشاركة الملف: $e');
      Get.snackbar(
        'خطأ',
        'فشل في مشاركة الملف',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }
}
