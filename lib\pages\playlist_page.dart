import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../ controllers/audio_controller.dart';
import '../ controllers/audio_playlist_controller.dart';
import '../ controllers/playlist_controller.dart dart.dart';
import 'playlist_videos_page.dart';
import '../model/media_item.dart';
import '../services/enhanced_audio_player_service.dart';

/// صفحة قوائم التشغيل الرئيسية مع التبويبات
/// تحتوي على تبويب للفيديو وتبويب للصوت
class PlaylistPage extends StatelessWidget {
  const PlaylistPage({super.key});

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 2,
      child: Scaffold(
        backgroundColor: Get.theme.scaffoldBackgroundColor,
        appBar: AppBar(
          title: Text(
            'قوائم التشغيل',
            style: TextStyle(
              color: Get.theme.colorScheme.onPrimary,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          backgroundColor: Colors.transparent,
          elevation: 0,
          flexibleSpace: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Get.theme.colorScheme.primary,
                  Get.theme.colorScheme.secondary,
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
          ),
          bottom: TabBar(
            indicatorColor: Get.theme.colorScheme.onPrimary,
            labelColor: Get.theme.colorScheme.onPrimary,
            unselectedLabelColor:
                Get.theme.colorScheme.onPrimary.withValues(alpha: 0.7),
            labelStyle:
                const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
            tabs: const [
              Tab(
                icon: Icon(Icons.music_note),
                text: 'قوائم الصوت',
              ),
              Tab(
                icon: Icon(Icons.video_library),
                text: 'قوائم الفيديو',
              ),
            ],
          ),
        ),
        body: TabBarView(
          children: [
            AudioPlaylistsTab(),
            VideoPlaylistsTab(),
          ],
        ),
      ),
    );
  }
}

/// تبويب قوائم تشغيل الفيديو
/// يعرض جميع قوائم تشغيل الفيديو مع إمكانية إدارتها
class VideoPlaylistsTab extends StatelessWidget {
  const VideoPlaylistsTab({super.key});

  @override
  Widget build(BuildContext context) {
    final playlistController = Get.find<PlaylistController>();
    final TextEditingController newPlaylistController = TextEditingController();

    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Obx(() {
        final playlists = playlistController.playlists;
        if (playlists.isEmpty) {
          return _buildEmptyState(
            icon: Icons.video_library_outlined,
            title: 'لا توجد قوائم فيديو',
            subtitle: 'أنشئ قائمة تشغيل جديدة لحفظ مقاطع الفيديو المفضلة',
            onPressed: () => _showAddPlaylistDialog(
                context, playlistController, newPlaylistController),
          );
        }
        return _buildPlaylistGrid(playlists, playlistController);
      }),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _showAddPlaylistDialog(
            context, playlistController, newPlaylistController),
        icon: const Icon(Icons.add),
        label: const Text('قائمة جديدة'),
        backgroundColor: Get.theme.colorScheme.primary,
      ),
    );
  }

  /// بناء حالة فارغة عندما لا توجد قوائم تشغيل
  Widget _buildEmptyState({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onPressed,
  }) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: Get.theme.colorScheme.primary.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                size: 64,
                color: Get.theme.colorScheme.primary,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              title,
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Get.theme.colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              subtitle,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16,
                color: Get.theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
            const SizedBox(height: 32),
            ElevatedButton.icon(
              onPressed: onPressed,
              icon: const Icon(Icons.add),
              label: const Text('إنشاء قائمة جديدة'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Get.theme.colorScheme.primary,
                foregroundColor: Get.theme.colorScheme.onPrimary,
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء شبكة قوائم التشغيل
  Widget _buildPlaylistGrid(List playlists, PlaylistController controller) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: GridView.builder(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 0.8,
        ),
        itemCount: playlists.length,
        itemBuilder: (context, index) {
          final playlist = playlists[index];
          return _buildPlaylistCard(playlist, index, controller);
        },
      ),
    );
  }

  /// بناء بطاقة قائمة التشغيل
  Widget _buildPlaylistCard(
      playlist, int index, PlaylistController controller) {
    return Builder(
      builder: (context) => Card(
        elevation: 8,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Get.theme.colorScheme.primary.withValues(alpha: 0.1),
                Get.theme.colorScheme.secondary.withValues(alpha: 0.1),
              ],
            ),
          ),
          child: InkWell(
            borderRadius: BorderRadius.circular(16),
            onTap: () {
              Get.to(() => PlaylistVideosPage(playlistIndex: index));
            },
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Get.theme.colorScheme.primary,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(
                          Icons.video_library,
                          color: Get.theme.colorScheme.onPrimary,
                          size: 24,
                        ),
                      ),
                      PopupMenuButton<String>(
                        icon: Icon(
                          Icons.more_vert,
                          color: Get.theme.colorScheme.onSurface,
                        ),
                        onSelected: (value) {
                          if (value == 'delete') {
                            _showDeleteConfirmation(context, controller, index);
                          } else if (value == 'rename') {
                            _showRenameDialog(
                                context, controller, index, playlist.name);
                          }
                        },
                        itemBuilder: (context) => [
                          const PopupMenuItem(
                            value: 'rename',
                            child: Row(
                              children: [
                                Icon(Icons.edit, size: 20),
                                SizedBox(width: 8),
                                Text('تعديل الاسم'),
                              ],
                            ),
                          ),
                          const PopupMenuItem(
                            value: 'delete',
                            child: Row(
                              children: [
                                Icon(Icons.delete, size: 20, color: Colors.red),
                                SizedBox(width: 8),
                                Text('حذف القائمة',
                                    style: TextStyle(color: Colors.red)),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          playlist.name,
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Get.theme.colorScheme.onSurface,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          '${playlist.videoPaths.length} فيديو',
                          style: TextStyle(
                            fontSize: 14,
                            color: Get.theme.colorScheme.onSurface
                                .withValues(alpha: 0.7),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 16),
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    decoration: BoxDecoration(
                      color:
                          Get.theme.colorScheme.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.play_arrow,
                          color: Get.theme.colorScheme.primary,
                          size: 20,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          'تشغيل',
                          style: TextStyle(
                            color: Get.theme.colorScheme.primary,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _showAddPlaylistDialog(BuildContext context,
      PlaylistController controller, TextEditingController textController) {
    Get.dialog(
      Dialog(
        backgroundColor: Get.theme.dialogBackgroundColor,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'إنشاء قائمة تشغيل جديدة',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Get.theme.colorScheme.onSurface,
                ),
              ),
              const SizedBox(height: 20),
              TextField(
                controller: textController,
                decoration: InputDecoration(
                  hintText: 'اسم القائمة',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide:
                        BorderSide(color: Get.theme.colorScheme.primary),
                  ),
                ),
              ),
              const SizedBox(height: 24),
              Row(
                children: [
                  Expanded(
                    child: TextButton(
                      onPressed: () {
                        textController.clear();
                        Get.back();
                      },
                      child: const Text('إلغاء'),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        final name = textController.text.trim();
                        if (name.isNotEmpty) {
                          controller.addPlaylist(name);
                          textController.clear();
                          Get.back();
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Get.theme.colorScheme.primary,
                        foregroundColor: Get.theme.colorScheme.onPrimary,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: const Text('إنشاء'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showDeleteConfirmation(
      BuildContext context, PlaylistController controller, int index) {
    Get.dialog(
      AlertDialog(
        backgroundColor: Get.theme.dialogBackgroundColor,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Text(
          'تأكيد الحذف',
          style: TextStyle(color: Get.theme.colorScheme.onSurface),
        ),
        content: Text(
          'هل أنت متأكد من حذف هذه القائمة؟',
          style: TextStyle(color: Get.theme.colorScheme.onSurface),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              controller.deletePlaylist(index);
              Get.back();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  void _showRenameDialog(BuildContext context, PlaylistController controller,
      int index, String currentName) {
    final renameController = TextEditingController(text: currentName);
    Get.defaultDialog(
      title: 'تعديل اسم القائمة',
      content: TextField(
        controller: renameController,
        decoration: const InputDecoration(hintText: 'الاسم الجديد'),
      ),
      onConfirm: () {
        final newName = renameController.text.trim();
        if (newName.isNotEmpty) {
          controller.renamePlaylist(index, newName);
          Get.back();
        }
      },
      onCancel: () {
        Get.back();
      },
      textConfirm: 'حفظ',
      textCancel: 'إلغاء',
    );
  }
}

/// تبويب قوائم تشغيل الصوت
/// يعرض جميع قوائم تشغيل الصوت مع إمكانية إدارتها
class AudioPlaylistsTab extends StatelessWidget {
  const AudioPlaylistsTab({super.key});

  @override
  Widget build(BuildContext context) {
    final audioPlaylistController = Get.put(AudioPlaylistController());
    final audioController = Get.find<AudioController>();

    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Obx(() {
        final playlists = audioPlaylistController.playlists;
        if (playlists.isEmpty) {
          return _buildEmptyState(
            icon: Icons.music_note_outlined,
            title: 'لا توجد قوائم صوت',
            subtitle: 'أنشئ قائمة تشغيل جديدة لحفظ الأغاني المفضلة',
            onPressed: () =>
                _createPlaylistDialog(context, audioPlaylistController),
          );
        }
        return _buildAudioPlaylistGrid(
            playlists, audioPlaylistController, audioController);
      }),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () =>
            _createPlaylistDialog(context, audioPlaylistController),
        icon: const Icon(Icons.add),
        label: const Text('قائمة جديدة'),
        backgroundColor: Get.theme.colorScheme.primary,
      ),
    );
  }

  /// بناء حالة فارغة عندما لا توجد قوائم تشغيل
  Widget _buildEmptyState({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onPressed,
  }) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: Get.theme.colorScheme.primary.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                size: 64,
                color: Get.theme.colorScheme.primary,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              title,
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Get.theme.colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              subtitle,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16,
                color: Get.theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
            const SizedBox(height: 32),
            ElevatedButton.icon(
              onPressed: onPressed,
              icon: const Icon(Icons.add),
              label: const Text('إنشاء قائمة جديدة'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Get.theme.colorScheme.primary,
                foregroundColor: Get.theme.colorScheme.onPrimary,
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء شبكة قوائم تشغيل الصوت
  Widget _buildAudioPlaylistGrid(List playlists,
      AudioPlaylistController controller, AudioController audioController) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: GridView.builder(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 0.8,
        ),
        itemCount: playlists.length,
        itemBuilder: (context, index) {
          final playlist = playlists[index];
          return _buildAudioPlaylistCard(
              playlist, index, controller, audioController, context);
        },
      ),
    );
  }

  /// بناء بطاقة قائمة تشغيل الصوت
  Widget _buildAudioPlaylistCard(
      playlist,
      int index,
      AudioPlaylistController controller,
      AudioController audioController,
      BuildContext context) {
    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Get.theme.colorScheme.secondary.withValues(alpha: 0.1),
              Get.theme.colorScheme.primary.withValues(alpha: 0.1),
            ],
          ),
        ),
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: () =>
              _showPlaylistSongs(context, playlist.songIds, audioController),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Get.theme.colorScheme.secondary,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.music_note,
                        color: Get.theme.colorScheme.onSecondary,
                        size: 24,
                      ),
                    ),
                    PopupMenuButton<String>(
                      icon: Icon(
                        Icons.more_vert,
                        color: Get.theme.colorScheme.onSurface,
                      ),
                      onSelected: (value) {
                        if (value == 'delete') {
                          _showDeleteConfirmation(context, controller, index);
                        } else if (value == 'rename') {
                          _renamePlaylistDialog(
                              context, index, playlist.name, controller);
                        }
                      },
                      itemBuilder: (context) => [
                        const PopupMenuItem(
                          value: 'rename',
                          child: Row(
                            children: [
                              Icon(Icons.edit, size: 20),
                              SizedBox(width: 8),
                              Text('تعديل الاسم'),
                            ],
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'delete',
                          child: Row(
                            children: [
                              Icon(Icons.delete, size: 20, color: Colors.red),
                              SizedBox(width: 8),
                              Text('حذف القائمة',
                                  style: TextStyle(color: Colors.red)),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        playlist.name,
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Get.theme.colorScheme.onSurface,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        '${playlist.songIds.length} أغنية',
                        style: TextStyle(
                          fontSize: 14,
                          color: Get.theme.colorScheme.onSurface
                              .withValues(alpha: 0.7),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  decoration: BoxDecoration(
                    color:
                        Get.theme.colorScheme.secondary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.play_arrow,
                        color: Get.theme.colorScheme.secondary,
                        size: 20,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        'تشغيل',
                        style: TextStyle(
                          color: Get.theme.colorScheme.secondary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _createPlaylistDialog(
      BuildContext context, AudioPlaylistController controller) {
    final nameController = TextEditingController();
    Get.dialog(
      Dialog(
        backgroundColor: Get.theme.dialogBackgroundColor,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'إنشاء قائمة تشغيل جديدة',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Get.theme.colorScheme.onSurface,
                ),
              ),
              const SizedBox(height: 20),
              TextField(
                controller: nameController,
                decoration: InputDecoration(
                  hintText: 'اسم القائمة',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide:
                        BorderSide(color: Get.theme.colorScheme.primary),
                  ),
                ),
              ),
              const SizedBox(height: 24),
              Row(
                children: [
                  Expanded(
                    child: TextButton(
                      onPressed: () => Get.back(),
                      child: const Text('إلغاء'),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        final name = nameController.text.trim();
                        if (name.isNotEmpty) {
                          controller.addPlaylist(name);
                          Get.back();
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Get.theme.colorScheme.primary,
                        foregroundColor: Get.theme.colorScheme.onPrimary,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: const Text('إنشاء'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showDeleteConfirmation(
      BuildContext context, AudioPlaylistController controller, int index) {
    Get.dialog(
      AlertDialog(
        backgroundColor: Get.theme.dialogBackgroundColor,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Text(
          'تأكيد الحذف',
          style: TextStyle(color: Get.theme.colorScheme.onSurface),
        ),
        content: Text(
          'هل أنت متأكد من حذف هذه القائمة؟',
          style: TextStyle(color: Get.theme.colorScheme.onSurface),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              controller.deletePlaylist(index);
              Get.back();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  void _renamePlaylistDialog(BuildContext context, int index,
      String currentName, AudioPlaylistController controller) {
    final nameController = TextEditingController(text: currentName);
    Get.dialog(
      Dialog(
        backgroundColor: Get.theme.dialogBackgroundColor,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'تعديل اسم القائمة',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Get.theme.colorScheme.onSurface,
                ),
              ),
              const SizedBox(height: 20),
              TextField(
                controller: nameController,
                decoration: InputDecoration(
                  hintText: 'الاسم الجديد',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide:
                        BorderSide(color: Get.theme.colorScheme.primary),
                  ),
                ),
              ),
              const SizedBox(height: 24),
              Row(
                children: [
                  Expanded(
                    child: TextButton(
                      onPressed: () => Get.back(),
                      child: const Text('إلغاء'),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        final name = nameController.text.trim();
                        if (name.isNotEmpty) {
                          controller.renamePlaylist(index, name);
                          Get.back();
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Get.theme.colorScheme.primary,
                        foregroundColor: Get.theme.colorScheme.onPrimary,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: const Text('حفظ'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showPlaylistSongs(BuildContext context, List<int> songIds,
      AudioController audioController) {
    final songs = audioController.allSongs
        .where((song) => songIds.contains(song.id))
        .toList();

    Get.bottomSheet(
      Container(
        padding: const EdgeInsets.all(16),
        height: Get.height * 0.6,
        decoration: BoxDecoration(
          color: Get.theme.cardColor,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          children: [
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Get.theme.colorScheme.onSurface.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 16),
            Text(
              "الأغاني في هذه القائمة",
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Get.theme.colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: songs.isEmpty
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.music_off,
                            size: 64,
                            color: Get.theme.colorScheme.onSurface
                                .withValues(alpha: 0.5),
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'لا توجد أغاني في هذه القائمة',
                            style: TextStyle(
                              fontSize: 16,
                              color: Get.theme.colorScheme.onSurface
                                  .withValues(alpha: 0.7),
                            ),
                          ),
                        ],
                      ),
                    )
                  : ListView.builder(
                      itemCount: songs.length,
                      itemBuilder: (context, index) {
                        final song = songs[index];
                        return Card(
                          margin: const EdgeInsets.symmetric(vertical: 4),
                          child: ListTile(
                            leading: Container(
                              width: 50,
                              height: 50,
                              decoration: BoxDecoration(
                                color: Get.theme.colorScheme.primary
                                    .withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Icon(
                                Icons.music_note,
                                color: Get.theme.colorScheme.primary,
                              ),
                            ),
                            title: Text(
                              song.title,
                              style: TextStyle(
                                fontWeight: FontWeight.w600,
                                color: Get.theme.colorScheme.onSurface,
                              ),
                            ),
                            subtitle: Text(
                              song.artist ?? "غير معروف",
                              style: TextStyle(
                                color: Get.theme.colorScheme.onSurface
                                    .withValues(alpha: 0.7),
                              ),
                            ),
                            trailing: Icon(
                              Icons.play_arrow,
                              color: Get.theme.colorScheme.primary,
                            ),
                            onTap: () {
                              // تحويل SongModel إلى MediaItem
                              final mediaItem = MediaItem(
                                id: song.id.toString(),
                                title: song.title,
                                path: song.data,
                                artist: song.artist,
                                album: song.album,
                                duration:
                                    Duration(milliseconds: song.duration ?? 0),
                                type: MediaType.audio,
                                size: song.size,
                                dateAdded: DateTime.fromMillisecondsSinceEpoch(
                                    song.dateAdded ?? 0),
                              );

                              // تحويل جميع الأغاني إلى MediaItem
                              final playlistItems = songs
                                  .map((s) => MediaItem(
                                        id: s.id.toString(),
                                        title: s.title,
                                        path: s.data,
                                        artist: s.artist,
                                        album: s.album,
                                        duration: Duration(
                                            milliseconds: s.duration ?? 0),
                                        type: MediaType.audio,
                                        size: s.size,
                                        dateAdded:
                                            DateTime.fromMillisecondsSinceEpoch(
                                                s.dateAdded ?? 0),
                                      ))
                                  .toList();

                              // استخدام النظام الموحد الجديد
                              final playerService =
                                  EnhancedAudioPlayerService.instance;
                              playerService.playMediaItem(
                                mediaItem,
                                playlist: playlistItems,
                                playlistName: 'قائمة التشغيل',
                              );

                              // عرض المشغل الكامل
                              playerService.setPlayerSize(fullScreen: true);
                              Get.back();
                            },
                          ),
                        );
                      },
                    ),
            ),
          ],
        ),
      ),
    );
  }
}
