import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:social_media_app/%20controllers/media_controller.dart';
import '../widgets/gradient_background.dart';
import '../widgets/enhanced_audio_list_item.dart';
import '../services/simple_audio_player_service.dart';
import '../services/database_service.dart';
import 'playlist_detail_page.dart';

class FavoritesPage extends StatelessWidget {
  const FavoritesPage({super.key});

  @override
  Widget build(BuildContext context) {
    final mediaController = Get.find<MediaController>();
    final databaseService = DatabaseService.instance;
    final playerService = SimpleAudioPlayerService.instance;

    return Scaffold(
      appBar: AppBar(
        title: const Text('قوائم التشغيل'),
        backgroundColor: Colors.blue.shade800,
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _showCreatePlaylistDialog(),
          ),
        ],
      ),
      body: GradientBackground(
        child: DefaultTabController(
          length: 3,
          child: Column(
            children: [
              const TabBar(
                tabs: [
                  Tab(text: 'المفضلة', icon: Icon(Icons.favorite)),
                  Tab(text: 'مؤخراً', icon: Icon(Icons.history)),
                  Tab(text: 'قوائمي', icon: Icon(Icons.queue_music)),
                ],
              ),
              Expanded(
                child: TabBarView(
                  children: [
                    _buildFavoritesTab(mediaController),
                    _buildRecentTab(mediaController),
                    _buildCustomPlaylistsTab(databaseService),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFavoritesTab(MediaController mediaController) {
    return Obx(() {
      final favorites = mediaController.favoriteAudio;
      if (favorites.isEmpty) {
        return const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.favorite_border, size: 64, color: Colors.grey),
              SizedBox(height: 16),
              Text('لا توجد أغاني مفضلة حتى الآن'),
            ],
          ),
        );
      }

      return ListView.builder(
        padding: const EdgeInsets.all(8),
        itemCount: favorites.length,
        itemBuilder: (context, index) {
          final item = favorites[index];
          return EnhancedAudioListItem(
            mediaItem: item,
            playlist: favorites,
            index: index,
            onTap: () {
              final playerService = SimpleAudioPlayerService.instance;
              playerService.playMediaItem(
                item,
                playlist: favorites,
                playlistName: 'المفضلة',
              );
            },
          );
        },
      );
    });
  }

  Widget _buildRecentTab(MediaController mediaController) {
    return Obx(() {
      final recent = mediaController.recentAudio;
      if (recent.isEmpty) {
        return const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.history, size: 64, color: Colors.grey),
              SizedBox(height: 16),
              Text('لا توجد أغاني مشغلة مؤخراً'),
            ],
          ),
        );
      }

      return ListView.builder(
        padding: const EdgeInsets.all(8),
        itemCount: recent.length,
        itemBuilder: (context, index) {
          final item = recent[index];
          return EnhancedAudioListItem(
            mediaItem: item,
            playlist: recent,
            index: index,
            onTap: () {
              final playerService = SimpleAudioPlayerService.instance;
              playerService.playMediaItem(
                item,
                playlist: recent,
                playlistName: 'المشغلة مؤخراً',
              );
            },
          );
        },
      );
    });
  }

  Widget _buildCustomPlaylistsTab(DatabaseService databaseService) {
    final mediaController = Get.find<MediaController>();

    return Obx(() {
      final playlists = databaseService.customPlaylists;

      return Column(
        children: [
          // الإجراءات السريعة
          _buildQuickActions(mediaController),

          // قوائم التشغيل
          Expanded(
            child: playlists.isEmpty
                ? const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.queue_music, size: 64, color: Colors.grey),
                        SizedBox(height: 16),
                        Text('لا توجد قوائم تشغيل مخصصة'),
                        SizedBox(height: 8),
                        Text('اضغط على + لإنشاء قائمة جديدة'),
                      ],
                    ),
                  )
                : ListView.builder(
                    padding: const EdgeInsets.all(8),
                    itemCount: playlists.length,
                    itemBuilder: (context, index) {
                      final playlist = playlists[index];
                      return Card(
                        child: ListTile(
                          leading: const CircleAvatar(
                            child: Icon(Icons.queue_music),
                          ),
                          title: Text(playlist.name),
                          subtitle: Text('${playlist.itemIds.length} ملف'),
                          trailing: PopupMenuButton(
                            itemBuilder: (context) => [
                              PopupMenuItem(
                                child: const ListTile(
                                  leading: Icon(Icons.edit),
                                  title: Text('تعديل'),
                                ),
                                onTap: () => _editPlaylist(playlist),
                              ),
                              PopupMenuItem(
                                child: const ListTile(
                                  leading: Icon(Icons.delete),
                                  title: Text('حذف'),
                                ),
                                onTap: () => _deletePlaylist(playlist.id),
                              ),
                            ],
                          ),
                          onTap: () => _openPlaylist(playlist),
                        ),
                      );
                    },
                  ),
          ),
        ],
      );
    });
  }

  void _showCreatePlaylistDialog() {
    final TextEditingController nameController = TextEditingController();

    Get.dialog(
      AlertDialog(
        title: const Text('إنشاء قائمة تشغيل جديدة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: nameController,
              decoration: const InputDecoration(
                labelText: 'اسم قائمة التشغيل',
                hintText: 'أدخل اسم القائمة',
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'بعد الإنشاء، يمكنك إضافة الأغاني من المشغل أو من قائمة الأغاني',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => _createEmptyPlaylist(nameController.text.trim()),
            child: const Text('إنشاء فارغة'),
          ),
          TextButton(
            onPressed: () =>
                _createPlaylistWithSongs(nameController.text.trim()),
            child: const Text('إنشاء واختيار أغاني'),
          ),
        ],
      ),
    );
  }

  void _createEmptyPlaylist(String name) async {
    if (name.isEmpty) {
      Get.snackbar('خطأ', 'يرجى إدخال اسم القائمة');
      return;
    }

    final databaseService = DatabaseService.instance;
    final success = await databaseService.createPlaylist(
      name,
      description: 'قائمة تشغيل مخصصة',
      type: 'audio',
    );

    Get.back();
    if (success) {
      Get.snackbar('تم', 'تم إنشاء قائمة التشغيل بنجاح');
    } else {
      Get.snackbar('خطأ', 'فشل في إنشاء قائمة التشغيل');
    }
  }

  void _createPlaylistWithSongs(String name) async {
    if (name.isEmpty) {
      Get.snackbar('خطأ', 'يرجى إدخال اسم القائمة');
      return;
    }

    Get.back(); // إغلاق الحوار الحالي

    // إنشاء القائمة أولاً
    final databaseService = DatabaseService.instance;
    final success = await databaseService.createPlaylist(
      name,
      description: 'قائمة تشغيل مخصصة',
      type: 'audio',
    );

    if (success) {
      // الحصول على القائمة المنشأة حديثاً
      final newPlaylist = databaseService.customPlaylists.last;

      // عرض حوار اختيار الأغاني
      _showSongSelectionDialog(newPlaylist.id, name);
    } else {
      Get.snackbar('خطأ', 'فشل في إنشاء قائمة التشغيل');
    }
  }

  void _showSongSelectionDialog(String playlistId, String playlistName) {
    final mediaController = Get.find<MediaController>();
    final selectedSongs = <String>[].obs;

    Get.dialog(
      AlertDialog(
        title: Text('إضافة أغاني إلى: $playlistName'),
        content: SizedBox(
          width: double.maxFinite,
          height: 400,
          child: Column(
            children: [
              Text(
                  'اختر الأغاني التي تريد إضافتها (${selectedSongs.length} محددة)'),
              const SizedBox(height: 8),
              Expanded(
                child: Obx(() => ListView.builder(
                      itemCount: mediaController.allAudioFiles.length,
                      itemBuilder: (context, index) {
                        final song = mediaController.allAudioFiles[index];
                        final isSelected = selectedSongs.contains(song.id);

                        return CheckboxListTile(
                          title: Text(song.title ?? 'مجهول'),
                          subtitle: Text(song.artist ?? 'مجهول'),
                          value: isSelected,
                          onChanged: (bool? value) {
                            if (value == true) {
                              selectedSongs.add(song.id ?? '');
                            } else {
                              selectedSongs.remove(song.id);
                            }
                          },
                        );
                      },
                    )),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () =>
                _addSelectedSongsToPlaylist(playlistId, selectedSongs),
            child: const Text('إضافة المحددة'),
          ),
        ],
      ),
    );
  }

  void _addSelectedSongsToPlaylist(
      String playlistId, List<String> songIds) async {
    final databaseService = DatabaseService.instance;
    int addedCount = 0;

    for (String songId in songIds) {
      final success = await databaseService.addToPlaylist(playlistId, songId);
      if (success) addedCount++;
    }

    Get.back();
    Get.snackbar('تم', 'تم إضافة $addedCount أغنية إلى القائمة');
  }

  void _editPlaylist(playlist) {
    // TODO: تنفيذ تعديل قائمة التشغيل
    Get.snackbar('قريباً', 'ميزة تعديل قائمة التشغيل ستكون متاحة قريباً');
  }

  void _deletePlaylist(String playlistId) {
    Get.dialog(
      AlertDialog(
        title: const Text('حذف قائمة التشغيل'),
        content: const Text('هل أنت متأكد من حذف هذه القائمة؟'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () async {
              final databaseService = DatabaseService.instance;
              final success = await databaseService.deletePlaylist(playlistId);

              Get.back();
              if (success) {
                Get.snackbar('تم', 'تم حذف قائمة التشغيل');
              } else {
                Get.snackbar('خطأ', 'فشل في حذف قائمة التشغيل');
              }
            },
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  void _openPlaylist(playlist) {
    Get.to(() => PlaylistDetailPage(
          playlistId: playlist.id,
          playlistName: playlist.name,
        ));
  }

  Widget _buildQuickActions(MediaController mediaController) {
    return Card(
      margin: const EdgeInsets.all(8),
      elevation: 8,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'الإجراءات السريعة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildActionButton(
                  icon: Icons.shuffle,
                  label: 'تشغيل عشوائي',
                  onTap: () => _shuffleAllSongs(mediaController),
                ),
                _buildActionButton(
                  icon: Icons.favorite,
                  label: 'المفضلة',
                  onTap: () => _showFavorites(),
                ),
                _buildActionButton(
                  icon: Icons.history,
                  label: 'المشغلة مؤخراً',
                  onTap: () => _showRecent(),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        decoration: BoxDecoration(
          color: Get.theme.colorScheme.primary.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: Get.theme.colorScheme.primary.withOpacity(0.3),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: Get.theme.colorScheme.primary),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: Get.theme.colorScheme.primary,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _shuffleAllSongs(MediaController mediaController) {
    final allSongs = mediaController.allAudioFiles;
    if (allSongs.isNotEmpty) {
      allSongs.shuffle();
      final playerService = SimpleAudioPlayerService.instance;
      playerService.playMediaItem(
        allSongs.first,
        playlist: allSongs,
        playlistName: 'تشغيل عشوائي',
      );
      playerService.isShuffleEnabled.value = true;
      Get.snackbar('تم', 'بدء التشغيل العشوائي لجميع الأغاني');
    }
  }

  void _showFavorites() {
    // التبديل إلى تبويب المفضلة
    DefaultTabController.of(Get.context!)?.animateTo(0);
  }

  void _showRecent() {
    // التبديل إلى تبويب المؤخرة
    DefaultTabController.of(Get.context!)?.animateTo(1);
  }
}
