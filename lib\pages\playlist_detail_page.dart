import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../model/media_item.dart';
import '../services/database_service.dart';
import '../services/simple_audio_player_service.dart';
import '../widgets/enhanced_audio_list_item.dart';
import '../widgets/gradient_background.dart';
import 'package:social_media_app/%20controllers/media_controller.dart';

class PlaylistDetailPage extends StatelessWidget {
  final String playlistId;
  final String playlistName;

  const PlaylistDetailPage({
    super.key,
    required this.playlistId,
    required this.playlistName,
  });

  @override
  Widget build(BuildContext context) {
    final databaseService = DatabaseService.instance;
    final playerService = SimpleAudioPlayerService.instance;

    return Scaffold(
      appBar: AppBar(
        title: Text(playlistName),
        actions: [
          PopupMenuButton(
            itemBuilder: (context) => [
              PopupMenuItem(
                child: const ListTile(
                  leading: Icon(Icons.shuffle),
                  title: Text('تشغيل عشوائي'),
                ),
                onTap: () => _shufflePlay(),
              ),
              PopupMenuItem(
                child: const ListTile(
                  leading: Icon(Icons.clear_all),
                  title: Text('مسح القائمة'),
                ),
                onTap: () => _clearPlaylist(),
              ),
            ],
          ),
        ],
      ),
      body: Stack(
        children: [
          // محتوى القائمة
          GradientBackground(
            child: Obx(() {
              final playlist = databaseService.customPlaylists
                  .firstWhereOrNull((p) => p.id == playlistId);

              if (playlist == null) {
                return const Center(
                  child: Text('قائمة التشغيل غير موجودة'),
                );
              }

              if (playlist.itemIds.isEmpty) {
                return const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.queue_music, size: 64, color: Colors.grey),
                      SizedBox(height: 16),
                      Text('قائمة التشغيل فارغة'),
                      SizedBox(height: 8),
                      Text('أضف بعض الأغاني لتبدأ التشغيل'),
                    ],
                  ),
                );
              }

              // الحصول على الملفات الفعلية من معرفاتها
              final playlistItems = _getPlaylistItems(playlist.itemIds);

              return Column(
                children: [
                  // معلومات القائمة
                  Container(
                    padding: const EdgeInsets.all(16),
                    color: Theme.of(context).primaryColor.withOpacity(0.1),
                    child: Row(
                      children: [
                        const Icon(Icons.queue_music),
                        const SizedBox(width: 8),
                        Text(
                          '${playlistItems.length} ملف',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        const Spacer(),
                        ElevatedButton.icon(
                          onPressed: playlistItems.isNotEmpty
                              ? () => _playAll(playlistItems)
                              : null,
                          icon: const Icon(Icons.play_arrow),
                          label: const Text('تشغيل الكل'),
                        ),
                      ],
                    ),
                  ),

                  // قائمة الملفات
                  Expanded(
                    child: ListView.builder(
                      padding: const EdgeInsets.all(8),
                      itemCount: playlistItems.length,
                      itemBuilder: (context, index) {
                        final item = playlistItems[index];
                        return EnhancedAudioListItem(
                          mediaItem: item,
                          playlist: playlistItems,
                          index: index,
                          onTap: () {
                            playerService.playMediaItem(
                              item,
                              playlist: playlistItems,
                              playlistName: playlistName,
                            );
                          },
                        );
                      },
                    ),
                  ),
                ],
              );
            }),
          ),
        ],
      ),
    );
  }

  List<MediaItem> _getPlaylistItems(List<String> itemIds) {
    final mediaController = Get.find<MediaController>();
    final allAudioFiles = mediaController.allAudioFiles;

    // البحث عن الملفات بناءً على معرفاتها
    final playlistItems = <MediaItem>[];
    for (String itemId in itemIds) {
      final item =
          allAudioFiles.firstWhereOrNull((audio) => audio.id == itemId);
      if (item != null) {
        playlistItems.add(item);
      }
    }

    return playlistItems;
  }

  void _playAll(List<MediaItem> items) {
    if (items.isNotEmpty) {
      final playerService = SimpleAudioPlayerService.instance;
      playerService.playMediaItem(
        items.first,
        playlist: items,
        playlistName: playlistName,
      );
    }
  }

  void _shufflePlay() {
    final playlist = DatabaseService.instance.customPlaylists
        .firstWhereOrNull((p) => p.id == playlistId);

    if (playlist != null && playlist.itemIds.isNotEmpty) {
      final items = _getPlaylistItems(playlist.itemIds);
      if (items.isNotEmpty) {
        items.shuffle();
        final playerService = SimpleAudioPlayerService.instance;
        playerService.playMediaItem(
          items.first,
          playlist: items,
          playlistName: '$playlistName (عشوائي)',
        );
        // تفعيل الوضع العشوائي
        playerService.isShuffleEnabled.value = true;
      }
    }
  }

  void _clearPlaylist() {
    Get.dialog(
      AlertDialog(
        title: const Text('مسح قائمة التشغيل'),
        content: const Text('هل أنت متأكد من مسح جميع الأغاني من هذه القائمة؟'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () async {
              final databaseService = DatabaseService.instance;
              // TODO: تنفيذ مسح محتويات القائمة
              Get.back();
              Get.snackbar('تم', 'تم مسح قائمة التشغيل');
            },
            child: const Text('مسح'),
          ),
        ],
      ),
    );
  }
}
