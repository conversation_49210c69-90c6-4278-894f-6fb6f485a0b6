import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../utils/responsive_helper.dart';
import '../model/media_item.dart';
import '../services/simple_database_service.dart';

/// قائمة خيارات الفيديو
class VideoOptionsMenu extends StatelessWidget {
  final MediaItem mediaItem;
  final VoidCallback onDelete;
  final VoidCallback onHide;
  final VoidCallback onEdit;
  final VoidCallback onAddToFavorites;
  final VoidCallback onAddToPlaylist;

  const VideoOptionsMenu({
    super.key,
    required this.mediaItem,
    required this.onDelete,
    required this.onHide,
    required this.onEdit,
    required this.onAddToFavorites,
    required this.onAddToPlaylist,
  });

  @override
  Widget build(BuildContext context) {
    final databaseService = DatabaseService.instance;

    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // مقبض السحب
          Container(
            width: 40,
            height: 4,
            margin: EdgeInsets.symmetric(
              vertical: ResponsiveHelper.getSpacing(context, SpacingType.sm),
            ),
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // معلومات الملف
          _buildFileInfo(context),

          Divider(
            height: ResponsiveHelper.getSpacing(context, SpacingType.lg),
            thickness: 1,
          ),

          // خيارات الملف
          _buildOptions(context, databaseService),

          SizedBox(
            height: ResponsiveHelper.getSpacing(context, SpacingType.lg),
          ),
        ],
      ),
    );
  }

  Widget _buildFileInfo(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(
        ResponsiveHelper.getSpacing(context, SpacingType.md),
      ),
      child: Row(
        children: [
          // صورة مصغرة للفيديو
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.video_library,
              color: Theme.of(context).primaryColor,
              size: ResponsiveHelper.getIconSize(context, IconSizeType.large),
            ),
          ),

          SizedBox(
            width: ResponsiveHelper.getSpacing(context, SpacingType.md),
          ),

          // معلومات الملف
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  mediaItem.title ?? 'مجهول',
                  style: TextStyle(
                    fontSize: ResponsiveHelper.getFontSize(
                      context,
                      FontSizeType.medium,
                    ),
                    fontWeight: FontWeight.bold,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                SizedBox(
                  height: ResponsiveHelper.getSpacing(context, SpacingType.xs),
                ),
                Text(
                  _getFileSize(),
                  style: TextStyle(
                    fontSize: ResponsiveHelper.getFontSize(
                      context,
                      FontSizeType.small,
                    ),
                    color: Colors.grey[600],
                  ),
                ),
                Text(
                  _formatDuration(mediaItem.duration ?? Duration.zero),
                  style: TextStyle(
                    fontSize: ResponsiveHelper.getFontSize(
                      context,
                      FontSizeType.small,
                    ),
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),

          // زر التحرير
          IconButton(
            onPressed: onEdit,
            icon: Icon(
              Icons.edit,
              color: Theme.of(context).primaryColor,
              size: ResponsiveHelper.getIconSize(context, IconSizeType.medium),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOptions(BuildContext context, DatabaseService databaseService) {
    return Column(
      children: [
        // إضافة للمفضلة
        Obx(() => _buildOptionTile(
              context,
              icon: databaseService.isFavorite(mediaItem.id ?? '')
                  ? Icons.favorite
                  : Icons.favorite_border,
              title: databaseService.isFavorite(mediaItem.id ?? '')
                  ? 'إزالة من المفضلة'
                  : 'إضافة للمفضلة',
              subtitle: 'حفظ في قائمة المفضلة',
              onTap: () {
                Get.back();
                if (databaseService.isFavorite(mediaItem.id ?? '')) {
                  databaseService.removeFromFavorites(mediaItem.id ?? '');
                  Get.snackbar('تم', 'تم إزالة الملف من المفضلة');
                } else {
                  onAddToFavorites();
                }
              },
              iconColor: databaseService.isFavorite(mediaItem.id ?? '')
                  ? Colors.red
                  : null,
            )),

        // إضافة لقائمة تشغيل
        _buildOptionTile(
          context,
          icon: Icons.playlist_add,
          title: 'إضافة لقائمة تشغيل',
          subtitle: 'إضافة لقائمة تشغيل مخصصة',
          onTap: () {
            Get.back();
            onAddToPlaylist();
          },
        ),

        // إخفاء الملف
        Obx(() => _buildOptionTile(
              context,
              icon: databaseService.isHidden(mediaItem.id ?? '')
                  ? Icons.visibility
                  : Icons.visibility_off,
              title: databaseService.isHidden(mediaItem.id ?? '')
                  ? 'إظهار الملف'
                  : 'إخفاء الملف',
              subtitle: databaseService.isHidden(mediaItem.id ?? '')
                  ? 'إظهار الملف في القائمة'
                  : 'إخفاء الملف من القائمة',
              onTap: () {
                Get.back();
                if (databaseService.isHidden(mediaItem.id ?? '')) {
                  databaseService.unhideItem(mediaItem.id ?? '');
                  Get.snackbar('تم', 'تم إظهار الملف');
                } else {
                  onHide();
                }
              },
            )),

        // مشاركة الملف
        _buildOptionTile(
          context,
          icon: Icons.share,
          title: 'مشاركة',
          subtitle: 'مشاركة الملف مع التطبيقات الأخرى',
          onTap: () {
            Get.back();
            _shareFile();
          },
        ),

        // معلومات الملف
        _buildOptionTile(
          context,
          icon: Icons.info_outline,
          title: 'معلومات الملف',
          subtitle: 'عرض تفاصيل الملف',
          onTap: () {
            Get.back();
            _showFileInfo(context);
          },
        ),

        // حذف الملف
        _buildOptionTile(
          context,
          icon: Icons.delete_outline,
          title: 'حذف الملف',
          subtitle: 'حذف الملف نهائياً من الجهاز',
          onTap: () {
            Get.back();
            onDelete();
          },
          iconColor: Colors.red,
          titleColor: Colors.red,
        ),
      ],
    );
  }

  Widget _buildOptionTile(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    Color? iconColor,
    Color? titleColor,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: iconColor ?? Theme.of(context).iconTheme.color,
        size: ResponsiveHelper.getIconSize(context, IconSizeType.medium),
      ),
      title: Text(
        title,
        style: TextStyle(
          fontSize: ResponsiveHelper.getFontSize(context, FontSizeType.medium),
          color: titleColor,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          fontSize: ResponsiveHelper.getFontSize(context, FontSizeType.small),
        ),
      ),
      onTap: onTap,
    );
  }

  String _getFileSize() {
    try {
      final file = File(mediaItem.path);
      final bytes = file.lengthSync();
      if (bytes < 1024) return '$bytes B';
      if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
      if (bytes < 1024 * 1024 * 1024) {
        return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
      }
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    } catch (e) {
      return 'غير معروف';
    }
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);

    if (hours > 0) {
      return '${twoDigits(hours)}:${twoDigits(minutes)}:${twoDigits(seconds)}';
    } else {
      return '${twoDigits(minutes)}:${twoDigits(seconds)}';
    }
  }

  void _shareFile() {
    // TODO: تنفيذ مشاركة الملف
    Get.snackbar('قريباً', 'ميزة المشاركة ستكون متاحة قريباً');
  }

  void _showFileInfo(BuildContext context) {
    Get.dialog(
      AlertDialog(
        title: const Text('معلومات الملف'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildInfoRow('الاسم:', mediaItem.title ?? 'مجهول'),
            _buildInfoRow('المسار:', mediaItem.path),
            _buildInfoRow('الحجم:', _getFileSize()),
            _buildInfoRow(
                'المدة:', _formatDuration(mediaItem.duration ?? Duration.zero)),
            _buildInfoRow('النوع:', 'فيديو'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 60,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(color: Colors.grey),
            ),
          ),
        ],
      ),
    );
  }
}
