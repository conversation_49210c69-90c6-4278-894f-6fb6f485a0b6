import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:video_player/video_player.dart';
import '../utils/responsive_helper.dart';
import '../model/media_item.dart';

/// مشغل الفيديو العائم (Picture in Picture)
class FloatingVideoPlayer extends StatefulWidget {
  final VideoPlayerController videoController;
  final MediaItem mediaItem;
  final VoidCallback onClose;
  final VoidCallback onMaximize;

  const FloatingVideoPlayer({
    super.key,
    required this.videoController,
    required this.mediaItem,
    required this.onClose,
    required this.onMaximize,
  });

  @override
  State<FloatingVideoPlayer> createState() => _FloatingVideoPlayerState();
}

class _FloatingVideoPlayerState extends State<FloatingVideoPlayer> {
  Offset _position = const Offset(20, 100);
  bool _isDragging = false;
  bool _showControls = false;

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final playerWidth = ResponsiveHelper.screenWidth(context) * 0.4;
    final playerHeight = playerWidth * 9 / 16; // نسبة 16:9

    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Stack(
        children: [
          // خلفية شفافة قابلة للنقر لإغلاق المشغل
          GestureDetector(
            onTap: widget.onClose,
            child: Container(
              width: double.infinity,
              height: double.infinity,
              color: Colors.black.withOpacity(0.3),
            ),
          ),
          
          // المشغل العائم
          Positioned(
            left: _position.dx,
            top: _position.dy,
            child: GestureDetector(
              onPanStart: (details) {
                _isDragging = true;
              },
              onPanUpdate: (details) {
                if (_isDragging) {
                  setState(() {
                    _position = Offset(
                      (_position.dx + details.delta.dx).clamp(
                        0.0,
                        screenSize.width - playerWidth,
                      ),
                      (_position.dy + details.delta.dy).clamp(
                        0.0,
                        screenSize.height - playerHeight,
                      ),
                    );
                  });
                }
              },
              onPanEnd: (details) {
                _isDragging = false;
              },
              onTap: () {
                setState(() {
                  _showControls = !_showControls;
                });
                
                // إخفاء الضوابط تلقائياً بعد 3 ثوان
                Future.delayed(const Duration(seconds: 3), () {
                  if (mounted) {
                    setState(() {
                      _showControls = false;
                    });
                  }
                });
              },
              child: Container(
                width: playerWidth,
                height: playerHeight,
                decoration: BoxDecoration(
                  color: Colors.black,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.5),
                      blurRadius: 10,
                      offset: const Offset(0, 5),
                    ),
                  ],
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: Stack(
                    children: [
                      // مشغل الفيديو
                      SizedBox(
                        width: playerWidth,
                        height: playerHeight,
                        child: VideoPlayer(widget.videoController),
                      ),
                      
                      // الضوابط
                      if (_showControls)
                        _buildFloatingControls(),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFloatingControls() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.black.withOpacity(0.7),
            Colors.transparent,
            Colors.black.withOpacity(0.7),
          ],
        ),
      ),
      child: Column(
        children: [
          // الضوابط العلوية
          Padding(
            padding: EdgeInsets.all(
              ResponsiveHelper.getSpacing(context, SpacingType.sm),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // عنوان الملف
                Expanded(
                  child: Text(
                    widget.mediaItem.title ?? 'مجهول',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: ResponsiveHelper.getFontSize(
                        context,
                        FontSizeType.small,
                      ),
                      fontWeight: FontWeight.bold,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                
                // أزرار التحكم
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    IconButton(
                      onPressed: widget.onMaximize,
                      icon: Icon(
                        Icons.fullscreen,
                        color: Colors.white,
                        size: ResponsiveHelper.getIconSize(
                          context,
                          IconSizeType.small,
                        ),
                      ),
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(),
                    ),
                    IconButton(
                      onPressed: widget.onClose,
                      icon: Icon(
                        Icons.close,
                        color: Colors.white,
                        size: ResponsiveHelper.getIconSize(
                          context,
                          IconSizeType.small,
                        ),
                      ),
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(),
                    ),
                  ],
                ),
              ],
            ),
          ),
          
          const Spacer(),
          
          // ضوابط التشغيل
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              IconButton(
                onPressed: () {
                  // TODO: التالي
                },
                icon: Icon(
                  Icons.skip_previous,
                  color: Colors.white,
                  size: ResponsiveHelper.getIconSize(
                    context,
                    IconSizeType.medium,
                  ),
                ),
                padding: EdgeInsets.zero,
              ),
              
              IconButton(
                onPressed: () {
                  if (widget.videoController.value.isPlaying) {
                    widget.videoController.pause();
                  } else {
                    widget.videoController.play();
                  }
                },
                icon: Icon(
                  widget.videoController.value.isPlaying
                      ? Icons.pause
                      : Icons.play_arrow,
                  color: Colors.white,
                  size: ResponsiveHelper.getIconSize(
                    context,
                    IconSizeType.large,
                  ),
                ),
                padding: EdgeInsets.zero,
              ),
              
              IconButton(
                onPressed: () {
                  // TODO: السابق
                },
                icon: Icon(
                  Icons.skip_next,
                  color: Colors.white,
                  size: ResponsiveHelper.getIconSize(
                    context,
                    IconSizeType.medium,
                  ),
                ),
                padding: EdgeInsets.zero,
              ),
            ],
          ),
          
          SizedBox(
            height: ResponsiveHelper.getSpacing(context, SpacingType.sm),
          ),
        ],
      ),
    );
  }
}
