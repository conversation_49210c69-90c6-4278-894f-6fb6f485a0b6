import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../models/database_models.dart';
import '../model/media_item.dart';

/// خدمة قاعدة البيانات المحلية المبسطة
class DatabaseService extends GetxController {
  static DatabaseService get instance => Get.find<DatabaseService>();

  late SharedPreferences _prefs;

  // قوائم تفاعلية
  var favorites = <FavoriteItem>[].obs;
  var hiddenItems = <HiddenItem>[].obs;
  var customPlaylists = <CustomPlaylist>[].obs;
  var playbackStats = <PlaybackStats>[].obs;
  var appSettings = Rxn<AppSettings>();

  @override
  Future<void> onInit() async {
    super.onInit();
    await _initializePrefs();
    await _loadData();
  }

  /// تهيئة SharedPreferences
  Future<void> _initializePrefs() async {
    _prefs = await SharedPreferences.getInstance();
  }

  /// تحميل البيانات
  Future<void> _loadData() async {
    // تحميل المفضلة
    final favoritesJson = _prefs.getStringList('favorites') ?? [];
    favorites.assignAll(
      favoritesJson
          .map((json) => FavoriteItem.fromMap(jsonDecode(json)))
          .toList(),
    );

    // تحميل الملفات المخفية
    final hiddenJson = _prefs.getStringList('hidden') ?? [];
    hiddenItems.assignAll(
      hiddenJson.map((json) => HiddenItem.fromMap(jsonDecode(json))).toList(),
    );

    // تحميل قوائم التشغيل المخصصة
    final playlistsJson = _prefs.getStringList('custom_playlists') ?? [];
    customPlaylists.assignAll(
      playlistsJson
          .map((json) => CustomPlaylist.fromMap(jsonDecode(json)))
          .toList(),
    );

    // تحميل إحصائيات التشغيل
    final statsJson = _prefs.getStringList('playback_stats') ?? [];
    playbackStats.assignAll(
      statsJson.map((json) => PlaybackStats.fromMap(jsonDecode(json))).toList(),
    );

    // تحميل الإعدادات
    final settingsJson = _prefs.getString('settings');
    if (settingsJson != null) {
      appSettings.value = AppSettings.fromMap(jsonDecode(settingsJson));
    } else {
      appSettings.value = AppSettings();
    }
  }

  // === إدارة المفضلة ===

  /// إضافة إلى المفضلة
  Future<bool> addToFavorites(MediaItem mediaItem) async {
    try {
      // التحقق من عدم وجود العنصر مسبقاً
      if (isFavorite(mediaItem.id ?? '')) {
        return false;
      }

      final favoriteItem = FavoriteItem.fromMediaItem(mediaItem);
      favorites.add(favoriteItem);
      await _saveFavorites();
      return true;
    } catch (e) {
      print('خطأ في إضافة المفضلة: $e');
      return false;
    }
  }

  /// إزالة من المفضلة
  Future<bool> removeFromFavorites(String itemId) async {
    try {
      favorites.removeWhere((item) => item.id == itemId);
      await _saveFavorites();
      return true;
    } catch (e) {
      print('خطأ في إزالة المفضلة: $e');
      return false;
    }
  }

  /// حفظ المفضلة
  Future<void> _saveFavorites() async {
    final favoritesJson =
        favorites.map((item) => jsonEncode(item.toMap())).toList();
    await _prefs.setStringList('favorites', favoritesJson);
  }

  /// التحقق من وجود العنصر في المفضلة
  bool isFavorite(String itemId) {
    return favorites.any((item) => item.id == itemId);
  }

  /// الحصول على المفضلة حسب النوع
  List<FavoriteItem> getFavoritesByType(String type) {
    return favorites.where((item) => item.type == type).toList();
  }

  // === إدارة الملفات المخفية ===

  /// إخفاء ملف
  Future<bool> hideItem(MediaItem mediaItem) async {
    try {
      // التحقق من عدم إخفاء العنصر مسبقاً
      if (isHidden(mediaItem.id ?? '')) {
        return false;
      }

      final hiddenItem = HiddenItem.fromMediaItem(mediaItem);
      hiddenItems.add(hiddenItem);
      await _saveHidden();
      return true;
    } catch (e) {
      print('خطأ في إخفاء الملف: $e');
      return false;
    }
  }

  /// إظهار ملف مخفي
  Future<bool> unhideItem(String itemId) async {
    try {
      hiddenItems.removeWhere((item) => item.id == itemId);
      await _saveHidden();
      return true;
    } catch (e) {
      print('خطأ في إظهار الملف: $e');
      return false;
    }
  }

  /// حفظ الملفات المخفية
  Future<void> _saveHidden() async {
    final hiddenJson =
        hiddenItems.map((item) => jsonEncode(item.toMap())).toList();
    await _prefs.setStringList('hidden', hiddenJson);
  }

  /// حفظ قوائم التشغيل المخصصة
  Future<void> _savePlaylists() async {
    final playlistsJson =
        customPlaylists.map((item) => jsonEncode(item.toMap())).toList();
    await _prefs.setStringList('custom_playlists', playlistsJson);
  }

  /// حفظ إحصائيات التشغيل
  Future<void> _saveStats() async {
    final statsJson =
        playbackStats.map((item) => jsonEncode(item.toMap())).toList();
    await _prefs.setStringList('playback_stats', statsJson);
  }

  /// التحقق من إخفاء العنصر
  bool isHidden(String itemId) {
    return hiddenItems.any((item) => item.id == itemId);
  }

  /// الحصول على الملفات المخفية حسب النوع
  List<HiddenItem> getHiddenByType(String type) {
    return hiddenItems.where((item) => item.type == type).toList();
  }

  // === إدارة قوائم التشغيل المخصصة ===

  /// إنشاء قائمة تشغيل جديدة
  Future<bool> createPlaylist(String name,
      {String description = '', String type = 'mixed'}) async {
    try {
      final id = DateTime.now().millisecondsSinceEpoch.toString();
      final playlist = CustomPlaylist(
        id: id,
        name: name,
        description: description,
        itemIds: [],
        dateCreated: DateTime.now(),
        dateModified: DateTime.now(),
        type: type,
      );

      customPlaylists.add(playlist);
      await _savePlaylists();
      return true;
    } catch (e) {
      print('خطأ في إنشاء قائمة التشغيل: $e');
      return false;
    }
  }

  /// إضافة عنصر إلى قائمة تشغيل
  Future<bool> addToPlaylist(String playlistId, String itemId) async {
    try {
      final index = customPlaylists.indexWhere((p) => p.id == playlistId);
      if (index == -1) return false;

      final playlist = customPlaylists[index];
      if (!playlist.itemIds.contains(itemId)) {
        playlist.itemIds.add(itemId);
        playlist.dateModified = DateTime.now();

        // تحديث القائمة التفاعلية
        customPlaylists[index] = playlist;
        await _savePlaylists();
      }
      return true;
    } catch (e) {
      print('خطأ في إضافة العنصر لقائمة التشغيل: $e');
      return false;
    }
  }

  /// إزالة عنصر من قائمة تشغيل
  Future<bool> removeFromPlaylist(String playlistId, String itemId) async {
    try {
      final index = customPlaylists.indexWhere((p) => p.id == playlistId);
      if (index == -1) return false;

      final playlist = customPlaylists[index];
      playlist.itemIds.remove(itemId);
      playlist.dateModified = DateTime.now();

      // تحديث القائمة التفاعلية
      customPlaylists[index] = playlist;
      await _savePlaylists();
      return true;
    } catch (e) {
      print('خطأ في إزالة العنصر من قائمة التشغيل: $e');
      return false;
    }
  }

  /// حذف قائمة تشغيل
  Future<bool> deletePlaylist(String playlistId) async {
    try {
      customPlaylists.removeWhere((playlist) => playlist.id == playlistId);
      await _savePlaylists();
      return true;
    } catch (e) {
      print('خطأ في حذف قائمة التشغيل: $e');
      return false;
    }
  }

  /// الحصول على قائمة تشغيل بالمعرف
  CustomPlaylist? getPlaylistById(String playlistId) {
    try {
      return customPlaylists.firstWhere((p) => p.id == playlistId);
    } catch (e) {
      return null;
    }
  }

  /// الحصول على قوائم التشغيل حسب النوع
  List<CustomPlaylist> getPlaylistsByType(String type) {
    return customPlaylists.where((playlist) => playlist.type == type).toList();
  }

  // === إدارة الإعدادات ===

  /// تحديث الإعدادات
  Future<bool> updateSettings(AppSettings settings) async {
    try {
      await _prefs.setString('settings', jsonEncode(settings.toMap()));
      appSettings.value = settings;
      return true;
    } catch (e) {
      print('خطأ في تحديث الإعدادات: $e');
      return false;
    }
  }

  // === إدارة الإحصائيات ===

  /// تسجيل تشغيل ملف
  Future<void> recordPlayback(String itemId) async {
    try {
      final index = playbackStats.indexWhere((s) => s.itemId == itemId);

      if (index != -1) {
        // تحديث الإحصائيات الموجودة
        final stats = playbackStats[index];
        stats.playCount++;
        stats.lastPlayed = DateTime.now();
        playbackStats[index] = stats;
      } else {
        // إنشاء إحصائيات جديدة
        final stats = PlaybackStats(
          itemId: itemId,
          playCount: 1,
          lastPlayed: DateTime.now(),
          firstPlayed: DateTime.now(),
        );
        playbackStats.add(stats);
      }

      await _saveStats();
    } catch (e) {
      print('خطأ في تسجيل الإحصائيات: $e');
    }
  }

  /// الحصول على الملفات الأكثر تشغيلاً
  List<PlaybackStats> getMostPlayed({int limit = 10}) {
    final allStats = List<PlaybackStats>.from(playbackStats);
    allStats.sort((a, b) => b.playCount.compareTo(a.playCount));
    return allStats.take(limit).toList();
  }

  /// الحصول على الملفات المشغلة مؤخراً
  List<PlaybackStats> getRecentlyPlayed({int limit = 10}) {
    final allStats = List<PlaybackStats>.from(playbackStats);
    allStats.sort((a, b) => b.lastPlayed.compareTo(a.lastPlayed));
    return allStats.take(limit).toList();
  }

  /// تنظيف البيانات القديمة
  Future<void> cleanupOldData() async {
    try {
      final thirtyDaysAgo = DateTime.now().subtract(const Duration(days: 30));

      // حذف الإحصائيات القديمة
      playbackStats
          .removeWhere((stats) => stats.lastPlayed.isBefore(thirtyDaysAgo));
      await _saveStats();
    } catch (e) {
      print('خطأ في تنظيف البيانات: $e');
    }
  }
}
