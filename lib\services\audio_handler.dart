import 'dart:async';
import 'package:audio_service/audio_service.dart';
import 'package:just_audio/just_audio.dart';
import 'package:rxdart/rxdart.dart';
// import '../model/media_item.dart' as local;
import 'package:audio_service/audio_service.dart' as audio_service;
import '../model/media_item.dart' as local;

/// AudioHandler محسن للتشغيل في الخلفية مع الإشعارات
class EnhancedAudioHandler extends BaseAudioHandler
    with QueueHand<PERSON>, SeekHandler {
  final AudioPlayer _audioPlayer = AudioPlayer();
  final BehaviorSubject<List<MediaItem>> _queueSubject =
      BehaviorSubject.seeded([]);
  final BehaviorSubject<MediaItem?> _mediaItemSubject =
      BehaviorSubject.seeded(null);
  final BehaviorSubject<Duration> _positionSubject =
      BehaviorSubject.seeded(Duration.zero);

  // متغيرات التحكم
  bool _shuffleEnabled = false;
  AudioServiceRepeatMode _repeatMode = AudioServiceRepeatMode.none;
  List<int> _shuffledIndices = [];
  int _currentIndex = 0;

  EnhancedAudioHandler() {
    _init();
  }

  /// تهيئة AudioHandler
  Future<void> _init() async {
    // ربط streams
    _audioPlayer.playbackEventStream.map(_transformEvent).pipe(playbackState);
    _audioPlayer.sequenceStateStream.listen(_updateMediaItem);
    _audioPlayer.positionStream.pipe(_positionSubject);

    // ربط queue
    _queueSubject.pipe(queue);
    _mediaItemSubject.pipe(mediaItem);

    // معالجة انتهاء التشغيل
    _audioPlayer.playerStateStream.listen((state) {
      if (state.processingState == ProcessingState.completed) {
        _handleTrackCompleted();
      }
    });
  }

  /// تحويل حالة التشغيل
  PlaybackState _transformEvent(PlaybackEvent event) {
    return PlaybackState(
      controls: [
        MediaControl.skipToPrevious,
        if (_audioPlayer.playing) MediaControl.pause else MediaControl.play,
        MediaControl.skipToNext,
        MediaControl.stop,
      ],
      systemActions: const {
        MediaAction.seek,
        MediaAction.seekForward,
        MediaAction.seekBackward,
      },
      androidCompactActionIndices: const [0, 1, 2],
      processingState: const {
        ProcessingState.idle: AudioProcessingState.idle,
        ProcessingState.loading: AudioProcessingState.loading,
        ProcessingState.buffering: AudioProcessingState.buffering,
        ProcessingState.ready: AudioProcessingState.ready,
        ProcessingState.completed: AudioProcessingState.completed,
      }[_audioPlayer.processingState]!,
      playing: _audioPlayer.playing,
      updatePosition: _audioPlayer.position,
      bufferedPosition: _audioPlayer.bufferedPosition,
      speed: _audioPlayer.speed,
      queueIndex: _currentIndex,
      repeatMode: _repeatMode,
      shuffleMode: _shuffleEnabled
          ? AudioServiceShuffleMode.all
          : AudioServiceShuffleMode.none,
    );
  }

  /// تحديث MediaItem الحالي
  void _updateMediaItem(SequenceState? sequenceState) {
    final item = sequenceState?.currentSource?.tag as MediaItem?;
    _mediaItemSubject.add(item);
  }

  /// معالجة انتهاء التشغيل
  void _handleTrackCompleted() {
    switch (_repeatMode) {
      case AudioServiceRepeatMode.one:
        _audioPlayer.seek(Duration.zero);
        _audioPlayer.play();
        break;
      case AudioServiceRepeatMode.all:
        skipToNext();
        break;
      case AudioServiceRepeatMode.none:
        if (_currentIndex < queue.value.length - 1) {
          skipToNext();
        } else {
          stop();
        }
        break;
      default:
        break;
    }
  }

  /// تشغيل قائمة من الملفات
 Future<void> playPlaylist(List<local.MediaItem> mediaItems,
    {int startIndex = 0}) async {
  try {
    final audioSources = <AudioSource>[];
    final mediaItemsList = <audio_service.MediaItem>[];

    for (final item in mediaItems) {
      final mediaItem = audio_service.MediaItem(
        id: item.id ?? '',
        album: item.album ?? 'مجهول',
        title: item.title ?? 'مجهول',
        artist: item.artist ?? 'مجهول',
        duration: item.duration,
        artUri:
            item.displayAlbum != null ? Uri.parse(item.displayAlbum!) : null,
        extras: {'path': item.path},
      );

      mediaItemsList.add(mediaItem);
      audioSources.add(AudioSource.uri(
        Uri.file(item.path),
        tag: mediaItem,
      ));
    }

    _queueSubject.add(mediaItemsList);
    _currentIndex = startIndex.clamp(0, mediaItems.length - 1);

    final playlist = ConcatenatingAudioSource(children: audioSources);
    await _audioPlayer.setAudioSource(playlist, initialIndex: _currentIndex);

    play();
  } catch (e) {
    print('خطأ في تشغيل القائمة: $e');
  }
}

  /// تشغيل ملف واحد
  // Future<void> playMediaItem(local.MediaItem item) async {
  //   await playPlaylist([item], startIndex: 0);
  // }
  // @override
  // Future<void> playMediaItem(MediaItem item) async {
  //   await playPlaylist([item], startIndex: 0);
  // }
@override
Future<void> playMediaItem(audio_service.MediaItem item) async {
  final path = item.extras?['path'];
  if (path != null) {
    final audioSource = AudioSource.uri(Uri.file(path), tag: item);
    await _audioPlayer.setAudioSource(audioSource);
    play();
  }
}
  @override
  Future<void> play() async {
    await _audioPlayer.play();
  }

  @override
  Future<void> pause() async {
    await _audioPlayer.pause();
  }

  @override
  Future<void> stop() async {
    await _audioPlayer.stop();
    await super.stop();
  }

  @override
  Future<void> seek(Duration position) async {
    await _audioPlayer.seek(position);
  }

  @override
  Future<void> skipToNext() async {
    if (queue.value.isEmpty) return;

    int nextIndex;
    if (_shuffleEnabled) {
      nextIndex = _getNextShuffledIndex();
    } else {
      nextIndex = (_currentIndex + 1) % queue.value.length;
    }

    await skipToQueueItem(nextIndex);
  }

  @override
  Future<void> skipToPrevious() async {
    if (queue.value.isEmpty) return;

    int prevIndex;
    if (_shuffleEnabled) {
      prevIndex = _getPreviousShuffledIndex();
    } else {
      prevIndex = _currentIndex - 1;
      if (prevIndex < 0) prevIndex = queue.value.length - 1;
    }

    await skipToQueueItem(prevIndex);
  }

  @override
  Future<void> skipToQueueItem(int index) async {
    if (index < 0 || index >= queue.value.length) return;

    _currentIndex = index;
    await _audioPlayer.seek(Duration.zero, index: index);
    play();
  }

  @override
  Future<void> setRepeatMode(AudioServiceRepeatMode repeatMode) async {
    _repeatMode = repeatMode;
    playbackState.add(playbackState.value.copyWith(repeatMode: repeatMode));
  }

  @override
  Future<void> setShuffleMode(AudioServiceShuffleMode shuffleMode) async {
    _shuffleEnabled = shuffleMode == AudioServiceShuffleMode.all;
    if (_shuffleEnabled) {
      _generateShuffledIndices();
    }
    playbackState.add(playbackState.value.copyWith(shuffleMode: shuffleMode));
  }

  /// توليد فهارس عشوائية
  void _generateShuffledIndices() {
    _shuffledIndices.clear();
    _shuffledIndices
        .addAll(List.generate(queue.value.length, (index) => index));
    _shuffledIndices.shuffle();
  }

  /// الحصول على الفهرس التالي في وضع العشوائي
  int _getNextShuffledIndex() {
    if (_shuffledIndices.isEmpty) _generateShuffledIndices();
    final currentShuffledIndex = _shuffledIndices.indexOf(_currentIndex);
    return _shuffledIndices[
        (currentShuffledIndex + 1) % _shuffledIndices.length];
  }

  /// الحصول على الفهرس السابق في وضع العشوائي
  int _getPreviousShuffledIndex() {
    if (_shuffledIndices.isEmpty) _generateShuffledIndices();
    final currentShuffledIndex = _shuffledIndices.indexOf(_currentIndex);
    int prevIndex = currentShuffledIndex - 1;
    if (prevIndex < 0) prevIndex = _shuffledIndices.length - 1;
    return _shuffledIndices[prevIndex];
  }

  /// الحصول على الموضع الحالي
  Stream<Duration> get positionStream => _positionSubject.stream;

  /// الحصول على MediaItem الحالي
  Stream<MediaItem?> get currentMediaItem => _mediaItemSubject.stream;

  /// الحصول على حالة التشغيل
  bool get isPlaying => _audioPlayer.playing;

  /// الحصول على المدة الإجمالية
  Duration? get duration => _audioPlayer.duration;

  /// الحصول على الموضع الحالي
  Duration get position => _audioPlayer.position;

  /// الحصول على الفهرس الحالي
  int get currentIndex => _currentIndex;

  /// تنظيف الموارد
  @override
  Future<void> onTaskRemoved() async {
    await stop();
  }

  @override
  Future<void> customAction(String name, [Map<String, dynamic>? extras]) async {
    switch (name) {
      case 'setSpeed':
        final speed = extras?['speed'] as double? ?? 1.0;
        await _audioPlayer.setSpeed(speed);
        break;
    }
  }

  /// إغلاق الموارد
  Future<void> dispose() async {
    await _audioPlayer.dispose();
    await _queueSubject.close();
    await _mediaItemSubject.close();
    await _positionSubject.close();
  }
}
