import 'dart:io';
import 'package:get/get.dart';
import 'package:audio_service/audio_service.dart';
import 'package:audio_session/audio_session.dart';
import '../model/media_item.dart' as local;
import 'audio_handler.dart';

/// خدمة تشغيل الصوت المحسنة مع دعم التشغيل في الخلفية والإشعارات
class EnhancedAudioPlayerService extends GetxController {
  static EnhancedAudioPlayerService get instance =>
      Get.find<EnhancedAudioPlayerService>();

  EnhancedAudioHandler? _audioHandler;

  // حالة التشغيل
  var isPlaying = false.obs;
  var isLoading = false.obs;
  var currentPosition = Duration.zero.obs;
  var totalDuration = Duration.zero.obs;
  var playbackSpeed = 1.0.obs;

  // حالة واجهة المشغل (مرئي، مصغر، كامل)
  var isPlayerVisible = false.obs;
  var isMinimized = true.obs;
  var isFullScreen = false.obs;

  // قائمة التشغيل والفهرس الحالي
  var currentPlaylist = <local.MediaItem>[].obs;
  var currentIndex = 0.obs;
  var currentMediaItem = Rxn<local.MediaItem>();

  // إعدادات التشغيل
  var repeatMode = RepeatMode.none.obs;
  var isShuffleEnabled = false.obs;
  var shuffledIndices = <int>[].obs;

  // إعدادات الفرز والفلترة
  var sortOrder = SortOrder.name.obs;
  var isAscending = true.obs;
  var currentPlaylistName = 'جميع الأغاني'.obs;

  @override
  void onInit() {
    super.onInit();
    _initializeAudioService();
  }

  @override
  void onClose() {
    _audioHandler?.dispose();
    super.onClose();
  }

  /// تهيئة خدمة الصوت
  Future<void> _initializeAudioService() async {
    try {
      // تهيئة جلسة الصوت
      final session = await AudioSession.instance;
      await session.configure(const AudioSessionConfiguration.music());

      // إنشاء AudioHandler
      _audioHandler = await AudioService.init(
        builder: () => EnhancedAudioHandler(),
        config: const AudioServiceConfig(
          androidNotificationChannelId:
              'com.example.social_media_app.channel.audio',
          androidNotificationChannelName: 'تشغيل صوتي',
          androidNotificationOngoing: true,
          androidStopForegroundOnPause: true,
          androidNotificationIcon: 'mipmap/ic_launcher',
        ),
      );

      // ربط الـ streams
      _setupStreams();
    } catch (e) {
      print('خطأ في تهيئة خدمة الصوت: $e');
      // في حالة فشل AudioService، استخدم just_audio مباشرة
      await _initializeFallbackPlayer();
    }
  }

  /// تهيئة مشغل احتياطي في حالة فشل AudioService
  Future<void> _initializeFallbackPlayer() async {
    try {
      _audioHandler = EnhancedAudioHandler();
      _setupStreams();
      print('تم تهيئة المشغل الاحتياطي بنجاح');
    } catch (e) {
      print('خطأ في تهيئة المشغل الاحتياطي: $e');
    }
  }

  /// ربط الـ streams
  void _setupStreams() {
    if (_audioHandler == null) return;

    // ربط حالة التشغيل
    _audioHandler!.playbackState.listen((state) {
      isPlaying.value = state.playing;
      isLoading.value = state.processingState == AudioProcessingState.loading;
      currentIndex.value = state.queueIndex ?? 0;

      // تحديث وضع التكرار والخلط
      _updateRepeatMode(state.repeatMode);
      _updateShuffleMode(state.shuffleMode);
    });

    // ربط الموضع
    _audioHandler!.positionStream.listen((position) {
      currentPosition.value = position;
    });

    // ربط MediaItem الحالي
    _audioHandler!.mediaItem.listen((mediaItem) {
      if (mediaItem != null) {
        final localItem = _findLocalMediaItem(mediaItem.id);
        if (localItem != null) {
          currentMediaItem.value = localItem;
          totalDuration.value = mediaItem.duration ?? Duration.zero;
        }
      }
    });
  }

  /// تحديث وضع التكرار
  void _updateRepeatMode(AudioServiceRepeatMode mode) {
    switch (mode) {
      case AudioServiceRepeatMode.none:
        repeatMode.value = RepeatMode.none;
        break;
      case AudioServiceRepeatMode.one:
        repeatMode.value = RepeatMode.one;
        break;
      case AudioServiceRepeatMode.all:
        repeatMode.value = RepeatMode.all;
        break;
      default:
        repeatMode.value = RepeatMode.none;
    }
  }

  /// تحديث وضع الخلط
  void _updateShuffleMode(AudioServiceShuffleMode mode) {
    isShuffleEnabled.value = mode == AudioServiceShuffleMode.all;
  }

  /// البحث عن MediaItem محلي بالمعرف
  local.MediaItem? _findLocalMediaItem(String id) {
    try {
      return currentPlaylist.firstWhere((item) => item.id == id);
    } catch (e) {
      return null;
    }
  }

  /// تشغيل ملف صوتي
  Future<void> playMediaItem(local.MediaItem item,
      {List<local.MediaItem>? playlist, String? playlistName}) async {
    try {
      isLoading.value = true;

      // التحقق من وجود الملف
      final file = File(item.path);
      if (!await file.exists()) {
        Get.snackbar(
          'خطأ',
          'الملف غير موجود: ${item.title}',
          snackPosition: SnackPosition.BOTTOM,
        );
        return;
      }

      if (playlist != null) {
        currentPlaylist.assignAll(playlist);
        currentIndex.value =
            playlist.indexWhere((media) => media.id == item.id);
        if (currentIndex.value == -1) currentIndex.value = 0;
      }

      if (playlistName != null) currentPlaylistName.value = playlistName;

      // تشغيل باستخدام AudioHandler
      if (_audioHandler != null) {
        if (playlist != null) {
          await _audioHandler!
              .playPlaylist(playlist, startIndex: currentIndex.value);
        } else {
          await _audioHandler!.playMediaItem(item as MediaItem);
          currentPlaylist.assignAll([item]);
          currentIndex.value = 0;
        }
      }

      showPlayer();
    } catch (e) {
      print('خطأ في تشغيل الملف: $e');
      Get.snackbar(
        'خطأ في التشغيل',
        'فشل في تشغيل الملف: ${item.title}',
        snackPosition: SnackPosition.BOTTOM,
        duration: const Duration(seconds: 3),
      );
    } finally {
      isLoading.value = false;
    }
  }

  /// التبديل بين تشغيل/إيقاف مؤقت
  Future<void> togglePlayPause() async {
    if (_audioHandler != null) {
      if (isPlaying.value) {
        await _audioHandler!.pause();
      } else {
        await _audioHandler!.play();
      }
    }
  }

  /// تشغيل الملف التالي
  Future<void> playNext() async {
    if (_audioHandler != null) {
      await _audioHandler!.skipToNext();
    }
  }

  /// تشغيل الملف السابق
  Future<void> playPrevious() async {
    if (_audioHandler != null) {
      await _audioHandler!.skipToPrevious();
    }
  }

  /// الانتقال إلى موضع معين
  Future<void> seekTo(Duration position) async {
    if (_audioHandler != null) {
      await _audioHandler!.seek(position);
    }
  }

  /// تغيير سرعة التشغيل
  Future<void> setPlaybackSpeed(double speed) async {
    playbackSpeed.value = speed;
    if (_audioHandler != null) {
      await _audioHandler!.customAction('setSpeed', {'speed': speed});
    }
  }

  /// تبديل وضع التكرار
  Future<void> toggleRepeatMode() async {
    if (_audioHandler != null) {
      AudioServiceRepeatMode newMode;
      switch (repeatMode.value) {
        case RepeatMode.none:
          newMode = AudioServiceRepeatMode.one;
          break;
        case RepeatMode.one:
          newMode = AudioServiceRepeatMode.all;
          break;
        case RepeatMode.all:
          newMode = AudioServiceRepeatMode.none;
          break;
      }
      await _audioHandler!.setRepeatMode(newMode);
    }
  }

  /// تبديل وضع الخلط
  Future<void> toggleShuffle() async {
    if (_audioHandler != null) {
      final newMode = isShuffleEnabled.value
          ? AudioServiceShuffleMode.none
          : AudioServiceShuffleMode.all;
      await _audioHandler!.setShuffleMode(newMode);
    }
  }

  /// إيقاف التشغيل
  Future<void> stop() async {
    if (_audioHandler != null) {
      await _audioHandler!.stop();
    }
    hidePlayer();
  }

  /// إظهار المشغل
  void showPlayer() {
    isPlayerVisible.value = true;
  }

  /// إخفاء المشغل
  void hidePlayer() {
    isPlayerVisible.value = false;
    isMinimized.value = true;
    isFullScreen.value = false;
  }

  /// تبديل حجم المشغل
  void togglePlayerSize() {
    if (isFullScreen.value) {
      // من كامل إلى مصغر
      isFullScreen.value = false;
      isMinimized.value = true;
    } else {
      // من مصغر إلى كامل
      isFullScreen.value = true;
      isMinimized.value = false;
    }
  }

  /// تعيين حجم المشغل
  void setPlayerSize({required bool fullScreen}) {
    isFullScreen.value = fullScreen;
    isMinimized.value = !fullScreen;
  }

  // Getters للمعلومات الحالية
  String get currentTitle => currentMediaItem.value?.title ?? '';
  String get currentArtist => currentMediaItem.value?.artist ?? 'مجهول';
  String get currentAlbum => currentMediaItem.value?.album ?? 'مجهول';
  bool get hasCurrentTrack => currentMediaItem.value != null;
  String get formattedPosition => _formatDuration(currentPosition.value);
  String get formattedDuration => _formatDuration(totalDuration.value);

  /// ترتيب قائمة التشغيل
  void sortPlaylist(SortOrder order) {
    switch (order) {
      case SortOrder.name:
        currentPlaylist
            .sort((a, b) => (a.title ?? '').compareTo(b.title ?? ''));
        break;
      case SortOrder.artist:
        currentPlaylist
            .sort((a, b) => (a.artist ?? '').compareTo(b.artist ?? ''));
        break;
      case SortOrder.album:
        currentPlaylist
            .sort((a, b) => (a.album ?? '').compareTo(b.album ?? ''));
        break;
      case SortOrder.duration:
        currentPlaylist.sort((a, b) =>
            (a.duration?.inSeconds ?? 0).compareTo(b.duration?.inSeconds ?? 0));
        break;
      case SortOrder.dateAdded:
        currentPlaylist.sort((a, b) => (a.dateAdded ?? DateTime.now())
            .compareTo(b.dateAdded ?? DateTime.now()));
        break;
    }

    // إعادة إنشاء قائمة التشغيل في AudioHandler
    if (_audioHandler != null && currentPlaylist.isNotEmpty) {
      _audioHandler!
          .playPlaylist(currentPlaylist, startIndex: currentIndex.value);
    }
  }

  /// تنسيق الوقت
  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }
}

/// أنواع التكرار
enum RepeatMode { none, one, all }

/// أنواع الترتيب
enum SortOrder { name, artist, album, duration, dateAdded }
