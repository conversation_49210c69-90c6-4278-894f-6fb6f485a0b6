import 'dart:io';
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:video_player/video_player.dart' as vp;
import '../utils/responsive_helper.dart';
import '../ controllers/video_player_controller.dart' as custom;
import '../services/simple_database_service.dart';
import '../model/media_item.dart';
import 'floating_video_player.dart';
import 'video_options_menu.dart';
import 'video_playlist_drawer.dart';

/// مشغل الفيديو المحسن مع جميع المميزات
class EnhancedVideoPlayer extends StatefulWidget {
  final MediaItem mediaItem;
  final List<MediaItem> playlist;
  final int currentIndex;

  const EnhancedVideoPlayer({
    super.key,
    required this.mediaItem,
    required this.playlist,
    required this.currentIndex,
  });

  @override
  State<EnhancedVideoPlayer> createState() => _EnhancedVideoPlayerState();
}

class _EnhancedVideoPlayerState extends State<EnhancedVideoPlayer>
    with TickerProviderStateMixin {
  late vp.VideoPlayerController _videoController;
  late AnimationController _controlsAnimationController;
  late AnimationController _fadeAnimationController;

  final _playerController = Get.put(custom.VideoPlayerController());
  final _databaseService = DatabaseService.instance;

  bool _isControlsVisible = true;
  bool _isLoading = true;
  bool _isScreenLocked = false;
  bool _isAudioMode = false;
  bool _showPlaylist = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializeVideoPlayer();
    _setupAutoHideControls();
  }

  void _initializeAnimations() {
    _controlsAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _fadeAnimationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _controlsAnimationController.forward();
    _fadeAnimationController.forward();
  }

  Future<void> _initializeVideoPlayer() async {
    try {
      _videoController =
          vp.VideoPlayerController.file(File(widget.mediaItem.path));
      await _videoController.initialize();

      // تشغيل تلقائي
      await _videoController.play();

      // تسجيل الإحصائيات
      await _databaseService.recordPlayback(widget.mediaItem.id ?? '');

      setState(() {
        _isLoading = false;
      });

      // مراقبة انتهاء الفيديو
      _videoController.addListener(_videoListener);
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      Get.snackbar('خطأ', 'فشل في تحميل الفيديو');
    }
  }

  void _videoListener() {
    if (_videoController.value.position >= _videoController.value.duration) {
      _playNext();
    }
  }

  void _setupAutoHideControls() {
    Timer.periodic(const Duration(seconds: 3), (timer) {
      if (_isControlsVisible && !_isScreenLocked) {
        _hideControls();
      }
    });
  }

  void _showControls() {
    setState(() {
      _isControlsVisible = true;
    });
    _controlsAnimationController.forward();
  }

  void _hideControls() {
    setState(() {
      _isControlsVisible = false;
    });
    _controlsAnimationController.reverse();
  }

  void _toggleControls() {
    if (_isScreenLocked) return;

    if (_isControlsVisible) {
      _hideControls();
    } else {
      _showControls();
    }
  }

  void _togglePlayPause() {
    if (_videoController.value.isPlaying) {
      _videoController.pause();
    } else {
      _videoController.play();
    }
    setState(() {});
  }

  void _playNext() {
    if (widget.currentIndex < widget.playlist.length - 1) {
      final nextItem = widget.playlist[widget.currentIndex + 1];
      Get.off(() => EnhancedVideoPlayer(
            mediaItem: nextItem,
            playlist: widget.playlist,
            currentIndex: widget.currentIndex + 1,
          ));
    }
  }

  void _playPrevious() {
    if (widget.currentIndex > 0) {
      final prevItem = widget.playlist[widget.currentIndex - 1];
      Get.off(() => EnhancedVideoPlayer(
            mediaItem: prevItem,
            playlist: widget.playlist,
            currentIndex: widget.currentIndex - 1,
          ));
    }
  }

  void _toggleScreenLock() {
    setState(() {
      _isScreenLocked = !_isScreenLocked;
    });

    if (_isScreenLocked) {
      _hideControls();
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersive);
    } else {
      _showControls();
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    }
  }

  void _toggleAudioMode() {
    setState(() {
      _isAudioMode = !_isAudioMode;
    });
  }

  void _openFloatingPlayer() {
    Get.to(() => FloatingVideoPlayer(
          videoController: _videoController,
          mediaItem: widget.mediaItem,
          onClose: () => Get.back(),
          onMaximize: () {
            Get.back();
            _showControls();
          },
        ));
  }

  void _showOptionsMenu() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => VideoOptionsMenu(
        mediaItem: widget.mediaItem,
        onDelete: () => _deleteFile(),
        onHide: () => _hideFile(),
        onEdit: () => _editFile(),
        onAddToFavorites: () => _addToFavorites(),
        onAddToPlaylist: () => _addToPlaylist(),
      ),
    );
  }

  Future<void> _deleteFile() async {
    final confirm = await Get.dialog<bool>(
      AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: const Text('هل تريد حذف هذا الملف نهائياً؟'),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Get.back(result: true),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirm == true) {
      try {
        await File(widget.mediaItem.path).delete();
        Get.back();
        Get.snackbar('تم', 'تم حذف الملف بنجاح');
      } catch (e) {
        Get.snackbar('خطأ', 'فشل في حذف الملف');
      }
    }
  }

  Future<void> _hideFile() async {
    final success = await _databaseService.hideItem(widget.mediaItem);
    if (success) {
      Get.back();
      Get.snackbar('تم', 'تم إخفاء الملف');
    } else {
      Get.snackbar('خطأ', 'فشل في إخفاء الملف');
    }
  }

  void _editFile() {
    // TODO: تنفيذ تحرير الملف
    Get.snackbar('قريباً', 'ميزة التحرير ستكون متاحة قريباً');
  }

  Future<void> _addToFavorites() async {
    final success = await _databaseService.addToFavorites(widget.mediaItem);
    if (success) {
      Get.snackbar('تم', 'تم إضافة الملف للمفضلة');
    } else {
      Get.snackbar('تنبيه', 'الملف موجود في المفضلة مسبقاً');
    }
  }

  void _addToPlaylist() {
    // TODO: عرض قائمة قوائم التشغيل للاختيار
    Get.snackbar('قريباً', 'ميزة إضافة لقائمة التشغيل ستكون متاحة قريباً');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _buildVideoPlayer(),
    );
  }

  Widget _buildVideoPlayer() {
    return GestureDetector(
      onTap: _toggleControls,
      child: Stack(
        children: [
          // مشغل الفيديو
          Center(
            child: _isAudioMode
                ? _buildAudioModeView()
                : AspectRatio(
                    aspectRatio: _videoController.value.aspectRatio,
                    child: vp.VideoPlayer(_videoController),
                  ),
          ),

          // الضوابط
          if (_isControlsVisible && !_isScreenLocked) _buildControls(),

          // قفل الشاشة
          if (_isScreenLocked) _buildScreenLockOverlay(),

          // قائمة التشغيل
          if (_showPlaylist)
            VideoPlaylistDrawer(
              playlist: widget.playlist,
              currentIndex: widget.currentIndex,
              onItemSelected: (index) {
                final selectedItem = widget.playlist[index];
                Get.off(() => EnhancedVideoPlayer(
                      mediaItem: selectedItem,
                      playlist: widget.playlist,
                      currentIndex: index,
                    ));
              },
              onClose: () => setState(() => _showPlaylist = false),
            ),
        ],
      ),
    );
  }

  Widget _buildAudioModeView() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.purple.withOpacity(0.3),
            Colors.blue.withOpacity(0.3),
          ],
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.music_video,
            size:
                ResponsiveHelper.getIconSize(context, IconSizeType.extraLarge) *
                    3,
            color: Colors.white,
          ),
          SizedBox(
              height: ResponsiveHelper.getSpacing(context, SpacingType.lg)),
          Text(
            widget.mediaItem.title ?? 'مجهول',
            style: TextStyle(
              color: Colors.white,
              fontSize:
                  ResponsiveHelper.getFontSize(context, FontSizeType.title),
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(
              height: ResponsiveHelper.getSpacing(context, SpacingType.sm)),
          Text(
            'وضع الصوت فقط',
            style: TextStyle(
              color: Colors.white70,
              fontSize:
                  ResponsiveHelper.getFontSize(context, FontSizeType.medium),
            ),
          ),
          SizedBox(
              height: ResponsiveHelper.getSpacing(context, SpacingType.xl)),
          ElevatedButton.icon(
            onPressed: _toggleAudioMode,
            icon: const Icon(Icons.videocam),
            label: const Text('العودة للفيديو'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.white.withOpacity(0.2),
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildControls() {
    return AnimatedBuilder(
      animation: _controlsAnimationController,
      builder: (context, child) {
        return Opacity(
          opacity: _controlsAnimationController.value,
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.black.withOpacity(0.7),
                  Colors.transparent,
                  Colors.transparent,
                  Colors.black.withOpacity(0.7),
                ],
              ),
            ),
            child: Column(
              children: [
                _buildTopControls(),
                const Spacer(),
                _buildCenterControls(),
                const Spacer(),
                _buildBottomControls(),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildTopControls() {
    return SafeArea(
      child: Padding(
        padding: EdgeInsets.all(
            ResponsiveHelper.getSpacing(context, SpacingType.md)),
        child: Row(
          children: [
            IconButton(
              onPressed: () => Get.back(),
              icon: Icon(
                Icons.arrow_back,
                color: Colors.white,
                size: ResponsiveHelper.getIconSize(context, IconSizeType.large),
              ),
            ),
            const Spacer(),
            // معلومات الملف
            Expanded(
              flex: 3,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    widget.mediaItem.title ?? 'مجهول',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: ResponsiveHelper.getFontSize(
                          context, FontSizeType.medium),
                      fontWeight: FontWeight.bold,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  Text(
                    '${widget.currentIndex + 1} من ${widget.playlist.length}',
                    style: TextStyle(
                      color: Colors.white70,
                      fontSize: ResponsiveHelper.getFontSize(
                          context, FontSizeType.small),
                    ),
                  ),
                ],
              ),
            ),
            const Spacer(),
            // أزرار التحكم العلوية
            IconButton(
              onPressed: _toggleScreenLock,
              icon: Icon(
                _isScreenLocked ? Icons.lock : Icons.lock_open,
                color: Colors.white,
                size:
                    ResponsiveHelper.getIconSize(context, IconSizeType.medium),
              ),
            ),
            IconButton(
              onPressed: _openFloatingPlayer,
              icon: Icon(
                Icons.picture_in_picture,
                color: Colors.white,
                size:
                    ResponsiveHelper.getIconSize(context, IconSizeType.medium),
              ),
            ),
            IconButton(
              onPressed: _showOptionsMenu,
              icon: Icon(
                Icons.more_vert,
                color: Colors.white,
                size:
                    ResponsiveHelper.getIconSize(context, IconSizeType.medium),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCenterControls() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        IconButton(
          onPressed: widget.currentIndex > 0 ? _playPrevious : null,
          icon: Icon(
            Icons.skip_previous,
            color: widget.currentIndex > 0 ? Colors.white : Colors.white38,
            size:
                ResponsiveHelper.getIconSize(context, IconSizeType.extraLarge),
          ),
        ),
        Container(
          decoration: const BoxDecoration(
            color: Colors.white24,
            shape: BoxShape.circle,
          ),
          child: IconButton(
            onPressed: _togglePlayPause,
            icon: Icon(
              _videoController.value.isPlaying ? Icons.pause : Icons.play_arrow,
              color: Colors.white,
              size: ResponsiveHelper.getIconSize(
                      context, IconSizeType.extraLarge) *
                  1.5,
            ),
          ),
        ),
        IconButton(
          onPressed: widget.currentIndex < widget.playlist.length - 1
              ? _playNext
              : null,
          icon: Icon(
            Icons.skip_next,
            color: widget.currentIndex < widget.playlist.length - 1
                ? Colors.white
                : Colors.white38,
            size:
                ResponsiveHelper.getIconSize(context, IconSizeType.extraLarge),
          ),
        ),
      ],
    );
  }

  Widget _buildBottomControls() {
    return SafeArea(
      child: Padding(
        padding: EdgeInsets.all(
            ResponsiveHelper.getSpacing(context, SpacingType.md)),
        child: Column(
          children: [
            // شريط التقدم
            _buildProgressBar(),
            SizedBox(
                height: ResponsiveHelper.getSpacing(context, SpacingType.sm)),
            // أزرار التحكم السفلية
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  onPressed: _toggleAudioMode,
                  icon: Icon(
                    _isAudioMode ? Icons.videocam : Icons.audiotrack,
                    color: _isAudioMode ? Colors.orange : Colors.white,
                    size: ResponsiveHelper.getIconSize(
                        context, IconSizeType.medium),
                  ),
                ),
                IconButton(
                  onPressed: () =>
                      setState(() => _showPlaylist = !_showPlaylist),
                  icon: Icon(
                    Icons.playlist_play,
                    color: Colors.white,
                    size: ResponsiveHelper.getIconSize(
                        context, IconSizeType.medium),
                  ),
                ),
                IconButton(
                  onPressed: () {
                    // TODO: إعدادات الجودة
                  },
                  icon: Icon(
                    Icons.settings,
                    color: Colors.white,
                    size: ResponsiveHelper.getIconSize(
                        context, IconSizeType.medium),
                  ),
                ),
                IconButton(
                  onPressed: () {
                    // TODO: ملء الشاشة
                  },
                  icon: Icon(
                    Icons.fullscreen,
                    color: Colors.white,
                    size: ResponsiveHelper.getIconSize(
                        context, IconSizeType.medium),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressBar() {
    return StreamBuilder<Duration?>(
      stream: Stream.periodic(const Duration(milliseconds: 100),
          (_) => _videoController.value.position),
      builder: (context, snapshot) {
        final position = snapshot.data ?? Duration.zero;
        final duration = _videoController.value.duration;

        return Column(
          children: [
            SliderTheme(
              data: SliderTheme.of(context).copyWith(
                activeTrackColor: Colors.red,
                inactiveTrackColor: Colors.white24,
                thumbColor: Colors.red,
                overlayColor: Colors.red.withOpacity(0.2),
                trackHeight: 3.0,
              ),
              child: Slider(
                value: position.inMilliseconds.toDouble(),
                max: duration.inMilliseconds.toDouble(),
                onChanged: (value) {
                  _videoController
                      .seekTo(Duration(milliseconds: value.toInt()));
                },
              ),
            ),
            Padding(
              padding: EdgeInsets.symmetric(
                horizontal:
                    ResponsiveHelper.getSpacing(context, SpacingType.md),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    _formatDuration(position),
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: ResponsiveHelper.getFontSize(
                          context, FontSizeType.small),
                    ),
                  ),
                  Text(
                    _formatDuration(duration),
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: ResponsiveHelper.getFontSize(
                          context, FontSizeType.small),
                    ),
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildScreenLockOverlay() {
    return Container(
      color: Colors.black54,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.lock,
              color: Colors.white,
              size: ResponsiveHelper.getIconSize(
                      context, IconSizeType.extraLarge) *
                  2,
            ),
            SizedBox(
                height: ResponsiveHelper.getSpacing(context, SpacingType.md)),
            Text(
              'الشاشة مقفلة',
              style: TextStyle(
                color: Colors.white,
                fontSize:
                    ResponsiveHelper.getFontSize(context, FontSizeType.large),
              ),
            ),
            SizedBox(
                height: ResponsiveHelper.getSpacing(context, SpacingType.sm)),
            Text(
              'اضغط مرتين لإلغاء القفل',
              style: TextStyle(
                color: Colors.white70,
                fontSize:
                    ResponsiveHelper.getFontSize(context, FontSizeType.medium),
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);

    if (hours > 0) {
      return '${twoDigits(hours)}:${twoDigits(minutes)}:${twoDigits(seconds)}';
    } else {
      return '${twoDigits(minutes)}:${twoDigits(seconds)}';
    }
  }

  @override
  void dispose() {
    _videoController.dispose();
    _controlsAnimationController.dispose();
    _fadeAnimationController.dispose();
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    super.dispose();
  }
}
