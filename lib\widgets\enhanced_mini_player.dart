import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:social_media_app/utils/responsive_helper.dart';
import 'package:social_media_app/widgets/enhanced_full_player.dart';
import '../services/simple_audio_player_service.dart';

class EnhancedMiniPlayer extends StatelessWidget {
  final SimpleAudioPlayerService _playerService =
      SimpleAudioPlayerService.instance;

  EnhancedMiniPlayer({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final current = _playerService.currentMediaItem.value;
      if (current == null) return const SizedBox();

      return Container(
        margin: EdgeInsets.only(
          bottom: MediaQuery.of(context).padding.bottom,
        ),
        child: GestureDetector(
          onTap: () {
            _showFullPlayer(context);
          },
          child: Container(
            height: ResponsiveHelper.miniPlayerHeight(context),
            padding: ResponsiveHelper.horizontalPadding(context),
            decoration: BoxDecoration(
              color: Colors.grey.shade900,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.25),
                  blurRadius: 6,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                // صورة الألبوم أو أيقونة افتراضية
                ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child:
                      current.thumbnail != null && current.thumbnail!.isNotEmpty
                          ? Image.memory(
                              current.thumbnail! as Uint8List,
                              width: ResponsiveHelper.width(context, 55),
                              height: ResponsiveHelper.height(context, 55),
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) {
                                return _defaultAlbumIcon();
                              },
                            )
                          : _defaultAlbumIcon(),
                ),
                SizedBox(width: ResponsiveHelper.width(context, 12)),
                // معلومات الأغنية مع constraints لتجنب التمدد الزائد
                Expanded(
                  child: ConstrainedBox(
                    constraints: BoxConstraints(
                      maxHeight: ResponsiveHelper.height(context, 55),
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          current.title,
                          style: TextStyle(
                            fontSize: ResponsiveHelper.textSize(
                                context, FontSizeType.small),
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        SizedBox(height: ResponsiveHelper.height(context, 4)),
                        Text(
                          current.artist ?? '',
                          style: TextStyle(
                            fontSize: ResponsiveHelper.textSize(
                                    context, FontSizeType.small) -
                                2,
                            color: Colors.grey[400],
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                ),
                SizedBox(width: ResponsiveHelper.width(context, 8)),
                // زر التشغيل / الإيقاف
                IconButton(
                  icon: Icon(
                    _playerService.isPlaying.value
                        ? Icons.pause
                        : Icons.play_arrow,
                    size: ResponsiveHelper.iconSize(context),
                    color: Colors.white,
                  ),
                  onPressed: () {
                    _playerService.togglePlayPause();
                  },
                  splashRadius: 24,
                  tooltip: _playerService.isPlaying.value ? 'إيقاف' : 'تشغيل',
                ),
                // زر الإغلاق (إيقاف التشغيل)
                IconButton(
                  icon: Icon(
                    Icons.close,
                    size: ResponsiveHelper.iconSize(context) - 2,
                    color: Colors.white70,
                  ),
                  onPressed: () {
                    _playerService.stop();
                  },
                  splashRadius: 20,
                  tooltip: 'إيقاف التشغيل',
                ),
              ],
            ),
          ),
        ),
      );
    });
  }

  Widget _defaultAlbumIcon() {
    return Container(
      width: 55,
      height: 55,
      color: Colors.grey.shade800,
      child: Icon(
        Icons.music_note,
        size: 32,
        color: Colors.white54,
      ),
    );
  }

  void _showFullPlayer(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (_) => DraggableScrollableSheet(
        initialChildSize: 0.9,
        minChildSize: 0.3,
        maxChildSize: 1.0,
        builder: (_, scrollController) {
          return SingleChildScrollView(
            controller: scrollController,
            child: const EnhancedFullPlayer(),
          );
        },
      ),
    );
  }
}
