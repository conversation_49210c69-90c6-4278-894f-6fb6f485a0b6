import 'package:get/get.dart';
import '../model/media_item.dart';

/// كنترولر مشغل الفيديو
class VideoPlayerController extends GetxController {
  static VideoPlayerController get instance => Get.find<VideoPlayerController>();

  // حالة التشغيل
  var isPlaying = false.obs;
  var isLoading = false.obs;
  var currentPosition = Duration.zero.obs;
  var totalDuration = Duration.zero.obs;
  var volume = 1.0.obs;
  var playbackSpeed = 1.0.obs;

  // حالة واجهة المشغل
  var isFullScreen = false.obs;
  var isControlsVisible = true.obs;
  var isScreenLocked = false.obs;
  var isAudioMode = false.obs;

  // قائمة التشغيل
  var currentPlaylist = <MediaItem>[].obs;
  var currentIndex = 0.obs;
  var currentMediaItem = Rxn<MediaItem>();

  // إعدادات التشغيل
  var repeatMode = RepeatMode.none.obs;
  var isShuffleEnabled = false.obs;
  var autoPlayNext = true.obs;

  /// تحديث حالة التشغيل
  void updatePlaybackState({
    bool? playing,
    Duration? position,
    Duration? duration,
    bool? loading,
  }) {
    if (playing != null) isPlaying.value = playing;
    if (position != null) currentPosition.value = position;
    if (duration != null) totalDuration.value = duration;
    if (loading != null) isLoading.value = loading;
  }

  /// تحديث قائمة التشغيل
  void updatePlaylist(List<MediaItem> playlist, int index) {
    currentPlaylist.assignAll(playlist);
    currentIndex.value = index;
    if (playlist.isNotEmpty && index < playlist.length) {
      currentMediaItem.value = playlist[index];
    }
  }

  /// التبديل بين أوضاع التكرار
  void toggleRepeatMode() {
    switch (repeatMode.value) {
      case RepeatMode.none:
        repeatMode.value = RepeatMode.one;
        break;
      case RepeatMode.one:
        repeatMode.value = RepeatMode.all;
        break;
      case RepeatMode.all:
        repeatMode.value = RepeatMode.none;
        break;
    }
  }

  /// تبديل وضع الخلط
  void toggleShuffle() {
    isShuffleEnabled.value = !isShuffleEnabled.value;
  }

  /// تبديل وضع الشاشة الكاملة
  void toggleFullScreen() {
    isFullScreen.value = !isFullScreen.value;
  }

  /// تبديل رؤية الضوابط
  void toggleControlsVisibility() {
    isControlsVisible.value = !isControlsVisible.value;
  }

  /// قفل/إلغاء قفل الشاشة
  void toggleScreenLock() {
    isScreenLocked.value = !isScreenLocked.value;
    if (isScreenLocked.value) {
      isControlsVisible.value = false;
    }
  }

  /// تبديل وضع الصوت فقط
  void toggleAudioMode() {
    isAudioMode.value = !isAudioMode.value;
  }

  /// تحديث مستوى الصوت
  void updateVolume(double newVolume) {
    volume.value = newVolume.clamp(0.0, 1.0);
  }

  /// تحديث سرعة التشغيل
  void updatePlaybackSpeed(double speed) {
    playbackSpeed.value = speed;
  }

  /// الحصول على الفهرس التالي
  int getNextIndex() {
    if (isShuffleEnabled.value) {
      // TODO: تنفيذ الخلط
      return (currentIndex.value + 1) % currentPlaylist.length;
    } else {
      return (currentIndex.value + 1) % currentPlaylist.length;
    }
  }

  /// الحصول على الفهرس السابق
  int getPreviousIndex() {
    if (isShuffleEnabled.value) {
      // TODO: تنفيذ الخلط
      return currentIndex.value - 1 < 0 
          ? currentPlaylist.length - 1 
          : currentIndex.value - 1;
    } else {
      return currentIndex.value - 1 < 0 
          ? currentPlaylist.length - 1 
          : currentIndex.value - 1;
    }
  }

  /// تنسيق المدة الزمنية
  String formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);
    
    if (hours > 0) {
      return '${twoDigits(hours)}:${twoDigits(minutes)}:${twoDigits(seconds)}';
    } else {
      return '${twoDigits(minutes)}:${twoDigits(seconds)}';
    }
  }

  @override
  void onClose() {
    // تنظيف الموارد
    super.onClose();
  }
}

/// أوضاع التكرار
enum RepeatMode { none, one, all }
