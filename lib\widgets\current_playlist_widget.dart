
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../services/simple_audio_player_service.dart';
import '../model/media_item.dart';
import '../utils/responsive_helper.dart';

/// ويدجت عرض القائمة الحالية
class CurrentPlaylistWidget extends StatelessWidget {
  const CurrentPlaylistWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final playerService = SimpleAudioPlayerService.instance;

    return Scaffold(
      appBar: AppBar(
        title: Obx(() => Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  playerService.currentPlaylistName.value,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                ),
                Text(
                  'قائمة التشغيل النشطة',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.white70,
                      ),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                ),
              ],
            )),
        actions: [
          // زر الترتيب
          PopupMenuButton<SortOrder>(
            icon: const Icon(Icons.sort),
            onSelected: (order) {
              playerService.sortPlaylist(order);
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: SortOrder.name,
                child: ListTile(
                  leading: Icon(Icons.sort_by_alpha),
                  title: Text('ترتيب حسب الاسم'),
                ),
              ),
              const PopupMenuItem(
                value: SortOrder.artist,
                child: ListTile(
                  leading: Icon(Icons.person),
                  title: Text('ترتيب حسب الفنان'),
                ),
              ),
              const PopupMenuItem(
                value: SortOrder.album,
                child: ListTile(
                  leading: Icon(Icons.album),
                  title: Text('ترتيب حسب الألبوم'),
                ),
              ),
              const PopupMenuItem(
                value: SortOrder.duration,
                child: ListTile(
                  leading: Icon(Icons.timer),
                  title: Text('ترتيب حسب المدة'),
                ),
              ),
            ],
          ),

          // زر الخلط العشوائي
          Obx(() => IconButton(
                icon: Icon(
                  Icons.shuffle,
                  color: playerService.isShuffleEnabled.value
                      ? Theme.of(context).primaryColor
                      : null,
                ),
                onPressed: () => playerService.toggleShuffle(),
              )),

          // زر التكرار
          Obx(() => IconButton(
                icon: Icon(_getRepeatIcon(playerService.repeatMode.value)),
                color: playerService.repeatMode.value != RepeatMode.none
                    ? Theme.of(context).primaryColor
                    : null,
                onPressed: () => playerService.toggleRepeatMode(),
              )),
        ],
      ),
      body: Obx(() {
        final playlist = playerService.currentPlaylist;
        final currentIndex = playerService.currentIndex.value;

        if (playlist.isEmpty) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.queue_music, size: 64, color: Colors.grey),
                SizedBox(height: 16),
                Text(
                  'لا توجد ملفات في القائمة',
                  style: TextStyle(fontSize: 18, color: Colors.grey),
                ),
              ],
            ),
          );
        }

        return Column(
          children: [
            // معلومات القائمة
            Container(
              padding: const EdgeInsets.all(16),
              color: Theme.of(context).primaryColor.withOpacity(0.1),
              child: Row(
                children: [
                  const Icon(Icons.queue_music),
                  const SizedBox(width: 8),
                  Text(
                    '${playlist.length} ملف',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const Spacer(),
                  Text(
                    _getTotalDuration(playlist),
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ],
              ),
            ),

            // قائمة الملفات
            Expanded(
              child: ListView.builder(
                itemCount: playlist.length,
                itemBuilder: (context, index) {
                  final item = playlist[index];
                  final isCurrentTrack = index == currentIndex;

                  return Container(
                    color: isCurrentTrack
                        ? Theme.of(context).primaryColor.withOpacity(0.2)
                        : null,
                    child: ListTile(
                      leading: Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: isCurrentTrack
                              ? Theme.of(context).primaryColor
                              : Colors.grey.withOpacity(0.3),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: isCurrentTrack
                            ? Obx(() => Icon(
                                  playerService.isPlaying.value
                                      ? Icons.pause
                                      : Icons.play_arrow,
                                  color: Colors.white,
                                ))
                            : Center(
                                child: Text(
                                  '${index + 1}',
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                      ),
                      title: Text(
                        item.title ?? 'مجهول',
                        style: TextStyle(
                          fontWeight: isCurrentTrack
                              ? FontWeight.bold
                              : FontWeight.normal,
                          color: isCurrentTrack
                              ? Theme.of(context).primaryColor
                              : null,
                        ),
                      ),
                      subtitle: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(item.artist ?? 'مجهول'),
                          if (item.duration != null)
                            Text(_formatDuration(item.duration!)),
                        ],
                      ),
                      trailing: PopupMenuButton(
                        itemBuilder: (context) => [
                          PopupMenuItem(
                            child: const ListTile(
                              leading: Icon(Icons.play_arrow),
                              title: Text('تشغيل'),
                            ),
                            onTap: () => _playTrack(index),
                          ),
                          PopupMenuItem(
                            child: const ListTile(
                              leading: Icon(Icons.remove),
                              title: Text('إزالة من القائمة'),
                            ),
                            onTap: () => _removeFromPlaylist(index),
                          ),
                        ],
                      ),
                      onTap: () => _playTrack(index),
                    ),
                  );
                },
              ),
            ),
          ],
        );
      }),
    );
  }

  IconData _getRepeatIcon(RepeatMode mode) {
    switch (mode) {
      case RepeatMode.none:
        return Icons.repeat;
      case RepeatMode.one:
        return Icons.repeat_one;
      case RepeatMode.all:
        return Icons.repeat;
    }
  }

  String _getTotalDuration(List<MediaItem> playlist) {
    final totalSeconds = playlist
        .where((item) => item.duration != null)
        .map((item) => item.duration!.inSeconds)
        .fold(0, (sum, duration) => sum + duration);

    return _formatDuration(Duration(seconds: totalSeconds));
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final hours = duration.inHours;
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));

    if (hours > 0) {
      return '$hours:$minutes:$seconds';
    } else {
      return '$minutes:$seconds';
    }
  }

  void _playTrack(int index) {
    final playerService = SimpleAudioPlayerService.instance;

    // استخدام الحارس الآمن للتشغيل
    playerService.safePlayAtIndex(index);
  }

  void _removeFromPlaylist(int index) {
    final playerService = SimpleAudioPlayerService.instance;
    final playlist = playerService.currentPlaylist;

    if (index >= 0 && index < playlist.length) {
      playlist.removeAt(index);

      // تعديل الفهرس الحالي إذا لزم الأمر
      if (playerService.currentIndex.value >= index) {
        playerService.currentIndex.value =
            (playerService.currentIndex.value - 1)
                .clamp(0, playlist.length - 1);
      }

      Get.snackbar('تم', 'تم إزالة الملف من القائمة');
    }
  }
}

