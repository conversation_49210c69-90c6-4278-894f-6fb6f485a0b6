import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:social_media_app/model/media_item.dart';

import '../ controllers/media_controller.dart';
import '../widgets/enhanced_mini_player.dart';
import '../widgets/enhanced_full_player.dart';
import '../services/simple_audio_player_service.dart';

class FoldersPage extends StatelessWidget {
  const FoldersPage({super.key});

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 2,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('المجلدات'),
          bottom: const TabBar(
            tabs: [
              Tab(text: 'فيديو'),
              Tab(text: 'صوت'),
            ],
          ),
        ),
        body: const TabBarView(
          children: [
            VideoFoldersTab(),
            AudioFoldersTab(),
          ],
        ),
      ),
    );
  }
}

class VideoFoldersTab extends StatelessWidget {
  const VideoFoldersTab({super.key});

  @override
  Widget build(BuildContext context) {
    final mediaController = Get.find<MediaController>();

    return Obx(() {
      if (mediaController.videoFolders.isEmpty) {
        return const Center(child: Text('لا توجد مجلدات فيديو'));
      }
      return ListView.builder(
        itemCount: mediaController.videoFolders.length,
        itemBuilder: (ctx, index) {
          final folder = mediaController.videoFolders[index];
          return Card(
            margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            child: ListTile(
              leading: const Icon(Icons.folder),
              title: Text(folder.path.split('/').last),
              subtitle: Text(folder.path),
              onTap: () {
                // افتح المجلد وعرض محتوياته
              },
            ),
          );
        },
      );
    });
  }
}

class AudioFoldersTab extends StatelessWidget {
  const AudioFoldersTab({super.key});

  @override
  Widget build(BuildContext context) {
    final mediaController = Get.find<MediaController>();

    return Obx(() {
      if (mediaController.audioFolders.isEmpty) {
        return const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.folder_open, size: 64, color: Colors.grey),
              SizedBox(height: 16),
              Text('لا توجد مجلدات صوت'),
            ],
          ),
        );
      }
      return ListView.builder(
        padding: const EdgeInsets.all(8),
        itemCount: mediaController.audioFolders.length,
        itemBuilder: (ctx, index) {
          final folder = mediaController.audioFolders[index];
          final folderName = folder.path.split('/').last;
          final audioFilesInFolder = mediaController.allAudioFiles
              .where((file) => file.path.startsWith(folder.path))
              .toList();

          return Card(
            margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            child: ListTile(
              leading: const CircleAvatar(
                child: Icon(Icons.folder_special),
              ),
              title: Text(folderName),
              subtitle: Text('${audioFilesInFolder.length} ملف صوتي'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () {
                _openAudioFolder(folderName, audioFilesInFolder);
              },
            ),
          );
        },
      );
    });
  }

  void _openAudioFolder(String folderName, List audioFiles) {
    if (audioFiles.isEmpty) {
      Get.snackbar('تنبيه', 'لا توجد ملفات صوتية في هذا المجلد');
      return;
    }

    // تشغيل أول ملف في المجلد كقائمة تشغيل
    final playerService = SimpleAudioPlayerService.instance;
    playerService.playMediaItem(
      audioFiles.first,
      playlist: audioFiles.cast<MediaItem>(),
      playlistName: 'مجلد: $folderName',
    );

    Get.snackbar(
      'تم بدء التشغيل',
      'تشغيل مجلد: $folderName (${audioFiles.length} ملف)',
    );
  }
}
