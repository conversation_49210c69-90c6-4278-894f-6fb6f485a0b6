import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../utils/responsive_helper.dart';
import '../model/media_item.dart';

/// درج قائمة تشغيل الفيديو
class VideoPlaylistDrawer extends StatelessWidget {
  final List<MediaItem> playlist;
  final int currentIndex;
  final Function(int) onItemSelected;
  final VoidCallback onClose;

  const VideoPlaylistDrawer({
    super.key,
    required this.playlist,
    required this.currentIndex,
    required this.onItemSelected,
    required this.onClose,
  });

  @override
  Widget build(BuildContext context) {
    return Positioned(
      right: 0,
      top: 0,
      bottom: 0,
      child: Container(
        width: ResponsiveHelper.screenWidth(context) * 0.8,
        decoration: BoxDecoration(
          color: Theme.of(context).scaffoldBackgroundColor,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.3),
              blurRadius: 10,
              offset: const Offset(-5, 0),
            ),
          ],
        ),
        child: Column(
          children: [
            // رأس القائمة
            _buildHeader(context),
            
            // قائمة الملفات
            Expanded(
              child: _buildPlaylist(context),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(
        ResponsiveHelper.getSpacing(context, SpacingType.md),
      ),
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor.withOpacity(0.1),
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).dividerColor,
            width: 1,
          ),
        ),
      ),
      child: SafeArea(
        child: Row(
          children: [
            Icon(
              Icons.playlist_play,
              color: Theme.of(context).primaryColor,
              size: ResponsiveHelper.getIconSize(context, IconSizeType.large),
            ),
            
            SizedBox(
              width: ResponsiveHelper.getSpacing(context, SpacingType.sm),
            ),
            
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'قائمة التشغيل',
                    style: TextStyle(
                      fontSize: ResponsiveHelper.getFontSize(
                        context,
                        FontSizeType.large,
                      ),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  
                  Text(
                    '${playlist.length} ملف',
                    style: TextStyle(
                      fontSize: ResponsiveHelper.getFontSize(
                        context,
                        FontSizeType.small,
                      ),
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            
            IconButton(
              onPressed: onClose,
              icon: Icon(
                Icons.close,
                size: ResponsiveHelper.getIconSize(context, IconSizeType.medium),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPlaylist(BuildContext context) {
    return ListView.builder(
      itemCount: playlist.length,
      itemBuilder: (context, index) {
        final item = playlist[index];
        final isCurrentItem = index == currentIndex;
        
        return Container(
          margin: EdgeInsets.symmetric(
            horizontal: ResponsiveHelper.getSpacing(context, SpacingType.sm),
            vertical: ResponsiveHelper.getSpacing(context, SpacingType.xs),
          ),
          decoration: BoxDecoration(
            color: isCurrentItem
                ? Theme.of(context).primaryColor.withOpacity(0.1)
                : null,
            borderRadius: BorderRadius.circular(8),
            border: isCurrentItem
                ? Border.all(
                    color: Theme.of(context).primaryColor,
                    width: 2,
                  )
                : null,
          ),
          child: ListTile(
            leading: Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Stack(
                children: [
                  Center(
                    child: Icon(
                      Icons.video_library,
                      color: Theme.of(context).primaryColor,
                      size: ResponsiveHelper.getIconSize(
                        context,
                        IconSizeType.medium,
                      ),
                    ),
                  ),
                  
                  if (isCurrentItem)
                    Positioned(
                      bottom: 2,
                      right: 2,
                      child: Container(
                        width: 16,
                        height: 16,
                        decoration: BoxDecoration(
                          color: Theme.of(context).primaryColor,
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.play_arrow,
                          color: Colors.white,
                          size: 12,
                        ),
                      ),
                    ),
                ],
              ),
            ),
            
            title: Text(
              item.title ?? 'مجهول',
              style: TextStyle(
                fontSize: ResponsiveHelper.getFontSize(
                  context,
                  FontSizeType.medium,
                ),
                fontWeight: isCurrentItem ? FontWeight.bold : FontWeight.normal,
                color: isCurrentItem ? Theme.of(context).primaryColor : null,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (item.duration != null)
                  Text(
                    _formatDuration(item.duration!),
                    style: TextStyle(
                      fontSize: ResponsiveHelper.getFontSize(
                        context,
                        FontSizeType.small,
                      ),
                      color: Colors.grey[600],
                    ),
                  ),
                
                Text(
                  'ملف ${index + 1}',
                  style: TextStyle(
                    fontSize: ResponsiveHelper.getFontSize(
                      context,
                      FontSizeType.small,
                    ),
                    color: Colors.grey[500],
                  ),
                ),
              ],
            ),
            
            trailing: isCurrentItem
                ? Icon(
                    Icons.equalizer,
                    color: Theme.of(context).primaryColor,
                    size: ResponsiveHelper.getIconSize(
                      context,
                      IconSizeType.medium,
                    ),
                  )
                : null,
            
            onTap: () => onItemSelected(index),
          ),
        );
      },
    );
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);
    
    if (hours > 0) {
      return '${twoDigits(hours)}:${twoDigits(minutes)}:${twoDigits(seconds)}';
    } else {
      return '${twoDigits(minutes)}:${twoDigits(seconds)}';
    }
  }
}
