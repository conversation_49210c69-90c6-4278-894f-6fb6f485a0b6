import 'package:flutter/material.dart';

/// مساعد التجاوب للتطبيق - يمكن استدعاؤه في أي مكان
class ResponsiveHelper {
  static ResponsiveHelper? _instance;
  static ResponsiveHelper get instance => _instance ??= ResponsiveHelper._();
  ResponsiveHelper._();

  /// الحصول على نوع الجهاز
  static DeviceType getDeviceType(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) {
      return DeviceType.mobile;
    } else if (screenWidth < 1200) {
      return DeviceType.tablet;
    } else {
      return DeviceType.desktop;
    }
  }

  /// الحصول على حجم الشاشة
  static ScreenSize getScreenSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 360) {
      return ScreenSize.small;
    } else if (screenWidth < 600) {
      return ScreenSize.medium;
    } else if (screenWidth < 900) {
      return ScreenSize.large;
    } else {
      return ScreenSize.extraLarge;
    }
  }

  /// الحصول على الاتجاه
  static bool isLandscape(BuildContext context) {
    return MediaQuery.of(context).orientation == Orientation.landscape;
  }

  /// الحصول على عرض الشاشة
  static double screenWidth(BuildContext context) {
    return MediaQuery.of(context).size.width;
  }

  /// الحصول على ارتفاع الشاشة
  static double screenHeight(BuildContext context) {
    return MediaQuery.of(context).size.height;
  }

  /// الحصول على نسبة العرض إلى الارتفاع
  static double aspectRatio(BuildContext context) {
    final size = MediaQuery.of(context).size;
    return size.width / size.height;
  }

  /// الحصول على حجم الخط المتجاوب مع تحسينات
  static double getFontSize(BuildContext context, FontSizeType type) {
    final screenSize = getScreenSize(context);
    final baseSize = _getBaseFontSize(type);

    switch (screenSize) {
      case ScreenSize.small:
        return baseSize * 0.85;
      case ScreenSize.medium:
        return baseSize;
      case ScreenSize.large:
        return baseSize * 1.25; // زيادة أكبر للنصوص في الشاشات الكبيرة
      case ScreenSize.extraLarge:
        return baseSize * 1.4; // تكبير إضافي للنصوص في الشاشات الضخمة
    }
  }

  static double _getBaseFontSize(FontSizeType type) {
    switch (type) {
      case FontSizeType.small:
        return 12.0;
      case FontSizeType.medium:
        return 14.0;
      case FontSizeType.large:
        return 16.0;
      case FontSizeType.extraLarge:
        return 18.0;
      case FontSizeType.title:
        return 20.0;
      case FontSizeType.headline:
        return 24.0;
    }
  }

  /// الحصول على المسافات المتجاوبة مع تحسينات
  static double getSpacing(BuildContext context, SpacingType type) {
    final screenSize = getScreenSize(context);
    final baseSpacing = _getBaseSpacing(type);

    switch (screenSize) {
      case ScreenSize.small:
        return baseSpacing * 0.75; // تقليل المسافات في الشاشات الصغيرة
      case ScreenSize.medium:
        return baseSpacing;
      case ScreenSize.large:
        return baseSpacing * 1.3; // زيادة المسافات قليلاً في الشاشات الكبيرة
      case ScreenSize.extraLarge:
        return baseSpacing * 1.5; // زيادة أكبر في الشاشات الكبيرة جداً
    }
  }

  static double _getBaseSpacing(SpacingType type) {
    switch (type) {
      case SpacingType.xs:
        return 4.0;
      case SpacingType.sm:
        return 8.0;
      case SpacingType.md:
        return 16.0;
      case SpacingType.lg:
        return 24.0;
      case SpacingType.xl:
        return 32.0;
      case SpacingType.xxl:
        return 48.0;
    }
  }

  /// الحصول على حجم الأيقونة المتجاوب مع تحسينات
  static double getIconSize(BuildContext context, IconSizeType type) {
    final screenSize = getScreenSize(context);
    final baseSize = _getBaseIconSize(type);

    switch (screenSize) {
      case ScreenSize.small:
        return baseSize * 0.85; // تقليل حجم الأيقونات في الشاشات الصغيرة
      case ScreenSize.medium:
        return baseSize;
      case ScreenSize.large:
        return baseSize * 1.2; // زيادة حجم الأيقونات في الشاشات الكبيرة
      case ScreenSize.extraLarge:
        return baseSize * 1.4; // زيادة أكبر في الشاشات الأكبر
    }
  }

  static double _getBaseIconSize(IconSizeType type) {
    switch (type) {
      case IconSizeType.small:
        return 16.0;
      case IconSizeType.medium:
        return 24.0;
      case IconSizeType.large:
        return 32.0;
      case IconSizeType.extraLarge:
        return 48.0;
    }
  }

  /// الحصول على عدد الأعمدة للشبكة مع تحسينات
  static int getGridColumns(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) {
      return 2; // موبايل
    } else if (screenWidth < 900) {
      return 3; // تابلت صغير
    } else if (screenWidth < 1200) {
      return 4; // تابلت كبير
    } else if (screenWidth < 1600) {
      return 5; // شاشة كبيرة دسكتوب
    } else {
      return 6; // شاشة فائقة الاتساع
    }
  }

  /// الحصول على نسبة العرض إلى الارتفاع للبطاقات مع تحسينات
  static double getCardAspectRatio(BuildContext context) {
    final deviceType = getDeviceType(context);
    switch (deviceType) {
      case DeviceType.mobile:
        return 0.75;
      case DeviceType.tablet:
        return 0.85; // زيادة النسبة قليلاً للتابلت
      case DeviceType.desktop:
        return 0.9; // نسبة أكبر للشاشات الكبيرة
    }
  }

  /// الحصول على حجم الزر المتجاوب مع تحسينات
  static Size getButtonSize(BuildContext context, ButtonSizeType type) {
    final screenSize = getScreenSize(context);
    final baseSize = _getBaseButtonSize(type);

    switch (screenSize) {
      case ScreenSize.small:
        return Size(baseSize.width * 0.85, baseSize.height * 0.85);
      case ScreenSize.medium:
        return baseSize;
      case ScreenSize.large:
        return Size(baseSize.width * 1.15, baseSize.height * 1.15);
      case ScreenSize.extraLarge:
        return Size(baseSize.width * 1.35, baseSize.height * 1.35);
    }
  }

  static Size _getBaseButtonSize(ButtonSizeType type) {
    switch (type) {
      case ButtonSizeType.small:
        return const Size(80, 32);
      case ButtonSizeType.medium:
        return const Size(120, 40);
      case ButtonSizeType.large:
        return const Size(160, 48);
      case ButtonSizeType.extraLarge:
        return const Size(200, 56);
    }
  }

  /// التحقق من إمكانية عرض الشريط الجانبي
  static bool canShowSidebar(BuildContext context) {
    return screenWidth(context) >= 900;
  }

  /// التحقق من إمكانية عرض القوائم المنسدلة
  static bool canShowDropdown(BuildContext context) {
    return screenWidth(context) >= 600;
  }

  /// ** تحكم إضافي **
  /// ارتفاع المشغل المصغر (Mini Player) متجاوب
  static double getMiniPlayerHeight(BuildContext context) {
    final screenSize = getScreenSize(context);
    switch (screenSize) {
      case ScreenSize.small:
        return 60;
      case ScreenSize.medium:
        return 70;
      case ScreenSize.large:
        return 90;
      case ScreenSize.extraLarge:
        return 110;
    }
  }

  /// ارتفاع أقصى للشاشة القابلة للسحب (مثل DraggableScrollableSheet)
  static double getMaxDraggableHeight(BuildContext context) {
    final height = screenHeight(context); // هنا غيرنا اسم المتغير ليصبح height
    final screenSize = getScreenSize(context);
    if (screenSize == ScreenSize.extraLarge) {
      return height * 0.85;
    }
    return height * 0.75;
  }

  /// ارتفاع المشغل المصغر المتجاوب
  static double miniPlayerHeight(BuildContext context) {
    final screenSize = getScreenSize(context);
    switch (screenSize) {
      case ScreenSize.small:
        return 70.0;
      case ScreenSize.medium:
        return 80.0;
      case ScreenSize.large:
        return 90.0;
      case ScreenSize.extraLarge:
        return 100.0;
    }
  }

  /// Padding أفقي متجاوب
  static EdgeInsets horizontalPadding(BuildContext context) {
    final screenSize = getScreenSize(context);
    switch (screenSize) {
      case ScreenSize.small:
        return const EdgeInsets.symmetric(horizontal: 8.0);
      case ScreenSize.medium:
        return const EdgeInsets.symmetric(horizontal: 12.0);
      case ScreenSize.large:
        return const EdgeInsets.symmetric(horizontal: 16.0);
      case ScreenSize.extraLarge:
        return const EdgeInsets.symmetric(horizontal: 20.0);
    }
  }

  /// حجم النص المتجاوب (يمكن تسمية أكثر تحديدًا لو تريد)
  static double textSize(BuildContext context, FontSizeType type) {
    return getFontSize(context, type);
  }

  static double iconSize(BuildContext context) {
    final width = screenWidth(context);
    if (width < 360) return 20;
    if (width < 480) return 24;
    return 28;
  }

  static double width(BuildContext context, double percent) {
  return screenWidth(context) * (percent / 100);
}

  static double height(BuildContext context, double percent) {
return screenHeight(context) * (percent / 100);  }
}

/// أنواع الأجهزة
enum DeviceType { mobile, tablet, desktop }

/// أحجام الشاشات
enum ScreenSize { small, medium, large, extraLarge }

/// أنواع أحجام الخطوط
enum FontSizeType { small, medium, large, extraLarge, title, headline }

/// أنواع المسافات
enum SpacingType { xs, sm, md, lg, xl, xxl }

/// أنواع أحجام الأيقونات
enum IconSizeType { small, medium, large, extraLarge }

/// أنواع أحجام الأزرار
enum ButtonSizeType { small, medium, large, extraLarge }

/// ويدجت مساعد للتجاوب
class ResponsiveWidget extends StatelessWidget {
  final Widget mobile;
  final Widget? tablet;
  final Widget? desktop;

  const ResponsiveWidget({
    super.key,
    required this.mobile,
    this.tablet,
    this.desktop,
  });

  @override
  Widget build(BuildContext context) {
    final deviceType = ResponsiveHelper.getDeviceType(context);

    switch (deviceType) {
      case DeviceType.mobile:
        return mobile;
      case DeviceType.tablet:
        return tablet ?? mobile;
      case DeviceType.desktop:
        return desktop ?? tablet ?? mobile;
    }
  }
}
